"use client";

import React, { useState, useCallback, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { Plus } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ImmigrationDocumentsDataTable } from "@/components/immigration-documents/immigration-documents-datatable";
import { CreateImmigrationDocumentDialog } from "@/components/immigration-documents/create-immigration-document-dialog";
import { useAuthErrorHandler } from "@/hooks/use-auth-error-handler";
import { apiUrl } from "@/utils/urls";

interface ImmigrationDocumentsPageProps {}

const ImmigrationDocumentsPage: React.FC<
  ImmigrationDocumentsPageProps
> = () => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { handle401Error } = useAuthErrorHandler();

  const [documents, setDocuments] = useState<IDocumentMaster[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  });
  const [filters, setFilters] = useState({
    search: "",
    category: "",
  });
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);

  const fetchDocuments = useCallback(
    async (page: number = 1, currentFilters = filters) => {
      if (status === "loading") return;
      if (!session) {
        router.push("/signin");
        return;
      }

      setLoading(true);
      try {
        const queryParams = new URLSearchParams({
          page: page.toString(),
          limit: pagination.limit.toString(),
          ...(currentFilters.search && { search: currentFilters.search }),
          ...(currentFilters.category && { category: currentFilters.category }),
        });

        const response = await fetch(
          `${apiUrl}/document-master?${queryParams}`,
          {
            headers: {
              Authorization: `Bearer ${session?.backendTokens.accessToken}`,
              "Content-Type": "application/json",
            },
          }
        );
        const result = await response.json();

        if (response.ok) {
          setDocuments(result.data || []);
          setPagination({
            page: result.page || page,
            limit: result.limit || 10,
            total: result.total || 0,
            totalPages: result.totalPages || 0,
          });
        } else {
          if (response.status === 401) {
            handle401Error();
            return;
          }
          toast.error(result.message || "Failed to fetch document masters");
          setDocuments([]);
        }
      } catch (error) {
        console.error("Error fetching document masters:", error);
        toast.error("Failed to fetch document masters");
        setDocuments([]);
      } finally {
        setLoading(false);
      }
    },
    [session, status, router, pagination.limit, filters, handle401Error]
  );

  useEffect(() => {
    fetchDocuments(1, filters);
  }, [filters]);

  const handleSearch = (search: string) => {
    setFilters((prev) => ({ ...prev, search }));
  };

  const handleCategoryFilter = (category: string) => {
    setFilters((prev) => ({ ...prev, category }));
  };

  const handlePageChange = (page: number) => {
    fetchDocuments(page, filters);
  };

  const handleCreateSuccess = () => {
    setIsCreateDialogOpen(false);
    fetchDocuments(pagination.page, filters);
  };

  const handleUpdateSuccess = () => {
    fetchDocuments(pagination.page, filters);
  };

  const handleDeleteSuccess = () => {
    fetchDocuments(pagination.page, filters);
  };

  if (status === "loading") {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Document Master</h1>
          <p className="text-muted-foreground">
            Manage document master templates for immigration services
          </p>
        </div>
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Add Document Master
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Document Master Templates</CardTitle>
        </CardHeader>
        <CardContent>
          <ImmigrationDocumentsDataTable
            data={documents}
            loading={loading}
            pagination={pagination}
            onPageChange={handlePageChange}
            onSearch={handleSearch}
            onCategoryFilter={handleCategoryFilter}
            onUpdateSuccess={handleUpdateSuccess}
            onDeleteSuccess={handleDeleteSuccess}
          />
        </CardContent>
      </Card>

      <CreateImmigrationDocumentDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        onSuccess={handleCreateSuccess}
      />
    </div>
  );
};

export default ImmigrationDocumentsPage;
