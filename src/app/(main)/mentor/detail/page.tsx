import React from "react";
import Link from "next/link";

import { ContentLayout } from "@/components/admin-panel/content-layout";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Content } from "@/components/common/content";
import { getMentorById } from "@/hooks/use-server";
import Detail from "@/components/mentor/detail";
import Services from "@/components/mentor/services";
import Reviews from "@/components/mentor/reviews";
import Payments from "@/components/mentor/payments";
import { Card, CardTitle } from "@/components/ui/card";
const MentorDetailPage = async ({
  searchParams,
}: {
  searchParams: { id: string };
}) => {
  const data = await getMentorById(searchParams.id);
  return (
    <ContentLayout title="Mentor">
      <div className="flex justify-between">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link href="/">Home</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink>
                <Link href="/mentor">Mentor</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Details</BreadcrumbPage>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>{data?.name}</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>
      <Content>
        {data && <Detail data={data} />}
        {data && <Services data={data} />}
        {data && <Reviews data={data} />}
        {data && <Payments data={data.services} />}

        <Card>
          <div className="flex items-center justify-between p-6">
            <CardTitle className="text-lg font-medium text-gray-700">
              Total Revenue
            </CardTitle>
            <div className="text-3xl font-bold text-primary">
              €{data?.total_revenue}
            </div>
          </div>
        </Card>
      </Content>
    </ContentLayout>
  );
};

export default MentorDetailPage;
