import React from "react";
import Link from "next/link";

import { ContentLayout } from "@/components/admin-panel/content-layout";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Content } from "@/components/common/content";
import { getUsers } from "@/hooks/use-server";
import UserDataTable from "@/components/table/user/user-datatable";
import { userUsColumns } from "@/components/table/user/user-columns";
import { Dialog, DialogTrigger } from "@/components/ui/dialog";
import { PlusIcon } from "lucide-react";
import UserForm from "@/components/user/user-form";

const UserPage = async () => {
  const data = await getUsers();
  return (
    <ContentLayout title="Users">
      <div className="flex justify-between">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link href="/">Home</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Users</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        <Dialog>
          <DialogTrigger className="bg-black p-2 text-white rounded-lg dark:bg-white dark:text-black">
            <PlusIcon />
          </DialogTrigger>
          <UserForm user={undefined} />
        </Dialog>
      </div>
      <Content>
        <UserDataTable data={data} columns={userUsColumns} />
      </Content>
    </ContentLayout>
  );
};

export default UserPage;
