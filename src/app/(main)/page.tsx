import React from "react";
import Link from "next/link";

import { ContentLayout } from "@/components/admin-panel/content-layout";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { getDashboard } from "@/hooks/use-server";
import { Content } from "@/components/common/content";
import Total from "@/components/dashboard/total";
import Users from "@/components/dashboard/users";
import Contact from "@/components/dashboard/contact";
import Breakdown from "@/components/dashboard/breakdown";
import Mentors from "@/components/dashboard/mentors";
import { RoleBasedRedirect } from "@/components/auth/role-based-redirect";

export default async function HomePage() {
  const data = await getDashboard();
  return (
    <ContentLayout title="Dashboard">
      <RoleBasedRedirect />
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/">Home</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Dashboard</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
      <Content>
        {data && <Total data={data} />}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
          {data && <Breakdown data={data} />}
          {data && <Mentors mentors={data.top_rated_mentors} />}
        </div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
          {data && <Users users={data.latest_users} />}
          {data && <Contact contacts={data.latest_contacts} />}
        </div>
      </Content>
    </ContentLayout>
  );
}
