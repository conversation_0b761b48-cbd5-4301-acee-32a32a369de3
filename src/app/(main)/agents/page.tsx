"use client";

import React, { useState, useCallback, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { Plus } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { AgentsDataTable } from "@/components/agents/agents-datatable";
import { CreateAgentDialog } from "@/components/agents/create-agent-dialog";
import { useAuthErrorHandler } from "@/hooks/use-auth-error-handler";
import { apiUrl } from "@/utils/urls";

interface AgentsPageProps {}

const AgentsPage: React.FC<AgentsPageProps> = () => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { handle401Error } = useAuthErrorHandler();

  const [agents, setAgents] = useState<IAgent[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  });
  const [filters, setFilters] = useState({
    search: "",
    status: "",
  });
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);

  const fetchAgents = useCallback(
    async (page: number = 1, currentFilters = filters) => {
      if (status === "loading") return;
      if (!session) {
        router.push("/signin");
        return;
      }

      setLoading(true);
      try {
        const queryParams = new URLSearchParams({
          page: page.toString(),
          limit: pagination.limit.toString(),
          ...(currentFilters.search && { search: currentFilters.search }),
          ...(currentFilters.status && { status: currentFilters.status }),
        });

        const response = await fetch(`${apiUrl}/agents?${queryParams}`, {
          headers: {
            Authorization: `Bearer ${session.backendTokens.accessToken}`,
            "Content-Type": "application/json",
          },
        });

        const result = await response.json();

        if (response.ok) {
          setAgents(result.data || []);
          setPagination({
            page: result.page || page,
            limit: result.limit || 10,
            total: result.total || 0,
            totalPages: result.totalPages || 0,
          });
        } else {
          if (response.status === 401) {
            handle401Error();
            return;
          }
          toast.error(result.message || "Failed to fetch agents");
          setAgents([]);
        }
      } catch (error) {
        console.error("Error fetching agents:", error);
        toast.error("Failed to fetch agents");
        setAgents([]);
      } finally {
        setLoading(false);
      }
    },
    [session, status, router, pagination.limit, filters, handle401Error]
  );

  useEffect(() => {
    fetchAgents(1, filters);
  }, [filters]);

  const handlePageChange = useCallback(
    (page: number) => {
      setPagination((prev) => ({ ...prev, page }));
      fetchAgents(page, filters);
    },
    [fetchAgents, filters]
  );

  const handleSearch = useCallback((search: string) => {
    setFilters((prev) => ({ ...prev, search }));
    setPagination((prev) => ({ ...prev, page: 1 }));
  }, []);

  const handleStatusFilter = useCallback((status: string) => {
    setFilters((prev) => ({ ...prev, status }));
    setPagination((prev) => ({ ...prev, page: 1 }));
  }, []);

  const handleRefresh = useCallback(() => {
    fetchAgents(pagination.page, filters);
  }, [fetchAgents, pagination.page, filters]);

  const handleCreateSuccess = useCallback(() => {
    setIsCreateDialogOpen(false);
    handleRefresh();
  }, [handleRefresh]);

  // Show loading state while session is loading
  if (status === "loading") {
    return (
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Loading...</p>
          </div>
        </div>
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!session) {
    router.push("/signin");
    return null;
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Agents</h1>
          <p className="text-muted-foreground">
            Manage and view all agent records
          </p>
        </div>
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Add Agent
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Agent Records</CardTitle>
        </CardHeader>
        <CardContent>
          <AgentsDataTable
            data={agents}
            loading={loading}
            pagination={pagination}
            onPageChange={handlePageChange}
            onSearch={handleSearch}
            onStatusFilter={handleStatusFilter}
            onRefresh={handleRefresh}
          />
        </CardContent>
      </Card>

      <CreateAgentDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        onSuccess={handleCreateSuccess}
      />
    </div>
  );
};

export default AgentsPage;
