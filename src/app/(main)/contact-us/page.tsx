import { getContactUs } from "@/hooks/use-server";
import React from "react";
import Link from "next/link";

import { ContentLayout } from "@/components/admin-panel/content-layout";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Content } from "@/components/common/content";
import ContactUsDataTable from "@/components/table/contact-us/contact-us-datatable";
import { contactUsColumns } from "@/components/table/contact-us/contact-us-columns";

const ContactUs = async () => {
  const data = await getContactUs();
  return (
    <ContentLayout title="Contact Us">
      <div className="flex justify-between">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link href="/">Home</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Contact Us</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>
      <Content>
        <ContactUsDataTable data={data} columns={contactUsColumns} />
      </Content>
    </ContentLayout>
  );
};

export default ContactUs;
