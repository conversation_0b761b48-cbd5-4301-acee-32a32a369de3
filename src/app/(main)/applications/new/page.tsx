"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import Link from "next/link";
import { ContentLayout } from "@/components/admin-panel/content-layout";
import {
  B<PERSON>crumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { ArrowLeft } from "lucide-react";
import { UserSelectionForm } from "@/components/applications/create/user-selection-form";
import { ProductSelectionForm } from "@/components/applications/create/product-selection-form";
import { DiscountApplicationForm } from "@/components/applications/create/discount-application-form";
import { PaymentSelectionForm } from "@/components/applications/create/payment-selection-form";
import { WorkflowSelectionForm } from "@/components/applications/create/workflow-selection-form";

import { useCreateApplication } from "@/hooks/use-query";
import { toast } from "sonner";

interface ApplicationFormData {
  // User data
  userId?: string;
  createNewUser: boolean;
  newUser?: any;

  // Product data
  immigrationProductId?: string;
  originalPrice: number;

  // Discount data
  discountAmount?: number;
  discountedPrice: number;

  // Payment data
  paymentMethod?: string;
  paymentType?: string;
  transactionId?: string; // Transaction ID for non-Stripe payments
  payments?: string[]; // Array of payment IDs
  additionalDiscountAmount?: number;

  // Workflow template data
  workflowTemplateId: string;

  // Agent assignment data
  assignedAgentId?: string;
  assignedAgents?: string[]; // Array of agent IDs
  priorityLevel: string;
  notes?: string;
}

/**
 * Application Creation Workflow Steps (v2.3.1 - 2025-07-09)
 *
 * Streamlined workflow with payment as the final step:
 *
 * Step 1: User Selection - Select existing user or create new user
 *   - If creating new user: API call to /api/user/register creates user and returns user ID
 *   - User ID is stored in formData.userId for use in subsequent steps
 * Step 2: Product Selection - Choose immigration service/product
 * Step 3: Discount Application - Apply discounts if needed
 * Step 4: Workflow Selection - Choose workflow template for selected product
 * Step 5: Payment Selection - Configure payment details and auto-create application
 *   - All payment methods: Auto-completes application and redirects to records
 *   - Uses existing user ID from Step 1 (no duplicate user creation)
 *
 * Key Changes (v2.3.1):
 * - Fixed duplicate user registration issue in submitApplication function
 * - Application creation now properly reuses user ID from Step 1
 * - Removed unnecessary user creation attempt during final submission
 *
 * Previous Changes (v2.3.0):
 * - Removed Agent Assignment step (Step 6) for streamlined workflow
 * - Payment is now the final step for all payment methods
 * - All payments auto-complete application creation with default agent assignment
 * - Simplified user flow: User → Service → Discount → Workflow → Payment → Complete
 * - Enhanced user experience with fewer steps and automatic completion
 */
const steps = [
  { id: 1, name: "User Selection", description: "Select or create user" },
  {
    id: 2,
    name: "Product Selection",
    description: "Choose immigration product",
  },
  {
    id: 3,
    name: "Discount Application",
    description: "Apply discount if needed",
  },
  {
    id: 4,
    name: "Workflow Selection",
    description: "Choose workflow template",
  },
  {
    id: 5,
    name: "Payment & Complete",
    description: "Process payment and create application",
  },
];

const CreateApplicationPage: React.FC = () => {
  const router = useRouter();
  const { status } = useSession();

  // Mutations
  const createApplicationMutation = useCreateApplication();

  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<Partial<ApplicationFormData>>({
    createNewUser: false,
    originalPrice: 0,
    discountedPrice: 0,
    workflowTemplateId: "",
    priorityLevel: "Medium",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const progress = (currentStep / steps.length) * 100;

  // Authentication check
  useEffect(() => {
    if (status === "loading") {
      return; // Still loading session
    }

    if (status === "unauthenticated") {
      // Redirect to signin with return URL
      const returnUrl = encodeURIComponent("/applications/new");
      router.push(`/signin?callbackUrl=${returnUrl}`);
      return;
    }
  }, [status, router]);

  // Show loading while checking authentication
  if (status === "loading") {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  // Don't render if not authenticated
  if (status === "unauthenticated") {
    return null;
  }

  const handleUserSelection = (data: any) => {
    // Validate that we have a valid userId
    if (!data.userId || data.userId.trim() === "") {
      toast.error("User selection failed", {
        description: "No valid user ID was provided. Please try again.",
      });
      return;
    }

    setFormData((prev) => ({
      ...prev,
      userId: data.userId,
      createNewUser: data.createNewUser,
      newUser: data.newUser,
    }));
    setCurrentStep(2);
  };

  const handleProductSelection = (data: any) => {
    setFormData((prev) => ({
      ...prev,
      immigrationProductId: data.immigrationProductId,
      originalPrice: data.originalPrice,
      discountedPrice: data.originalPrice, // Initialize with original price
    }));
    setCurrentStep(3);
  };

  /**
   * Handle discount application step completion
   * Stores pricing information and proceeds to workflow template selection (step 4)
   * @param {any} data - The discount application form data containing pricing information
   */
  const handleDiscountApplication = (data: any) => {
    setFormData((prev) => ({
      ...prev,
      originalPrice: data.originalPrice,
      discountAmount: data.discountAmount,
      discountedPrice: data.discountedPrice,
    }));
    setCurrentStep(4); // Now goes to Workflow Selection
  };

  /**
   * Handle payment selection step completion
   *
   * This function implements the streamlined workflow automation logic:
   * 1. For all payment methods: Auto-completes application with selected workflow template and redirects to records
   *
   * Auto-Completion Process:
   * - Uses the previously selected workflow template from step 4
   * - Creates application with default settings (unassigned agent, Medium priority)
   * - Redirects user to /applications page
   * - Handles errors gracefully with user-friendly error messages
   *
   * @param {any} data - The payment selection form data containing payment details
   * @param {string} data.paymentMethod - Payment method selected by user
   * @param {string} data.paymentType - Type of payment (Stripe, Cash, etc.)
   * @param {string} data.paymentId - Generated payment ID from API
   * @param {string[]} data.payments - Array of payment IDs
   * @param {string} data.transactionId - Transaction ID (optional)
   */
  const handlePaymentSelection = async (data: any) => {
    try {
      // Use the previously selected workflow template
      if (!formData.workflowTemplateId) {
        throw new Error("No workflow template selected");
      }

      // Prepare final application data with all collected information
      const finalFormData = {
        ...formData,
        paymentMethod: data.paymentMethod,
        paymentType: data.paymentType,
        transactionId: data.transactionId,
        payments: data.payments || [],
        workflowTemplateId: formData.workflowTemplateId,
        priorityLevel: "Medium", // Default priority
        notes: `Auto-created via ${data.paymentType} payment`,
      };

      await submitApplication(finalFormData as ApplicationFormData);

      // Redirect to application records page
      router.push("/applications");
    } catch (error) {
      console.error("Error auto-completing application:", error);
      // Handle error appropriately - could show error message to user
      // For now, we'll still redirect to applications page
      router.push("/applications");
    }
  };

  /**
   * Handle workflow template selection step completion
   *
   * This function stores the selected workflow template and proceeds to payment selection:
   * 1. Validates the selected workflow template ID
   * 2. Updates form data with the selected template
   * 3. Proceeds to payment selection step
   *
   * @param {any} data - Workflow selection form data
   * @param {string} data.workflowTemplateId - Selected workflow template ID
   */
  const handleWorkflowSelection = (data: any) => {
    const updatedFormData = {
      ...formData,
      workflowTemplateId: data.workflowTemplateId,
    };

    setFormData(updatedFormData);
    setCurrentStep(5); // Go to Payment Selection
  };

  /**
   * Submit Application - Final step to create application record
   *
   * IMPORTANT: This function should NOT attempt to create users again.
   * User creation/selection is handled in Step 1 (UserSelectionForm) and the
   * resulting user ID is stored in formData.userId throughout the workflow.
   *
   * Previous Issue (Fixed in v2.3.1):
   * - Function was attempting to create user again if createNewUser was true
   * - This caused duplicate user registration attempts and application creation failures
   * - Now properly reuses existing user ID from Step 1
   *
   * @param {ApplicationFormData} data - Complete application form data with user ID from Step 1
   */
  const submitApplication = async (data: ApplicationFormData) => {
    setIsSubmitting(true);
    try {
      // Use the existing user ID that was already created/selected in Step 1
      // No need to create user again - this prevents duplicate registration attempts
      const userId = data.userId;

      // Validate userId
      if (!userId || userId.trim() === "") {
        throw new Error(
          "User ID is missing. Cannot create application without a valid user."
        );
      }

      // Prepare the request payload using new API structure
      const payload = {
        service_type: "immigration",
        service_id: data.immigrationProductId || "", // Use immigration product ID as service ID
        user_id: userId,
        priority_level: data.priorityLevel,
        workflow_template_id: data.workflowTemplateId,
        payments: data.payments || [], // Use payments array from form data
        assigned_agent:
          data.assignedAgents ||
          (data.assignedAgentId ? [data.assignedAgentId] : undefined), // Convert single agent to array, optional
      };

      // Create the application
      await createApplicationMutation.mutateAsync(payload);

      toast.success("Application created successfully", {
        description:
          "The application has been created and you will be redirected to the applications list.",
      });

      // Redirect to applications list
      router.push("/applications");
    } catch (error) {
      console.error("Error creating application:", error);

      let errorMessage = "Failed to create application. Please try again.";
      if (error instanceof Error) {
        errorMessage = error.message;
      }

      toast.error("Application creation failed", {
        description: errorMessage,
      });

      // Error handling is done in the mutations
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <UserSelectionForm
            onNext={handleUserSelection}
            initialData={{
              userId: formData.userId,
              createNewUser: formData.createNewUser || false,
              newUser: formData.newUser,
            }}
          />
        );
      case 2:
        return (
          <ProductSelectionForm
            onNext={handleProductSelection}
            onBack={handleBack}
            initialData={{
              immigrationProductId: formData.immigrationProductId,
            }}
          />
        );
      case 3:
        return (
          <DiscountApplicationForm
            onNext={handleDiscountApplication}
            onBack={handleBack}
            initialData={{
              originalPrice: formData.originalPrice || 0,
              discountAmount: formData.discountAmount,
              discountedPrice:
                formData.discountedPrice || formData.originalPrice || 0,
            }}
          />
        );
      case 4:
        // Workflow Template Selection
        return (
          <WorkflowSelectionForm
            onNext={handleWorkflowSelection}
            onBack={handleBack}
            immigrationProductId={formData.immigrationProductId || ""}
            initialData={{
              workflowTemplateId: formData.workflowTemplateId,
            }}
          />
        );
      case 5:
        // Payment Selection - Final step that auto-creates application
        // Note: workflow_template_id is set from step 4

        // Validate required data before rendering payment form
        if (!formData.userId) {
          toast.error("Missing user information", {
            description: "Please go back and select or create a user.",
          });
          setCurrentStep(1);
          return null;
        }

        if (!formData.immigrationProductId) {
          toast.error("Missing service information", {
            description: "Please go back and select a service.",
          });
          setCurrentStep(2);
          return null;
        }

        if (!formData.workflowTemplateId) {
          toast.error("Missing workflow template", {
            description: "Please go back and select a workflow template.",
          });
          setCurrentStep(4);
          return null;
        }

        const applicationDataForPayment = {
          service_type: "immigration",
          service_id: formData.immigrationProductId || "",
          user_id: formData.userId || "",
          priority_level: "Medium", // Default priority for auto-completion
          workflow_template_id: formData.workflowTemplateId || "", // Set from workflow selection
          assigned_agent: [], // No agent assignment for streamlined workflow
        };

        return (
          <PaymentSelectionForm
            onNext={handlePaymentSelection}
            onBack={handleBack}
            originalPrice={formData.originalPrice || 0}
            discountedPrice={formData.discountedPrice || 0}
            userId={formData.userId || ""}
            immigrationServiceId={formData.immigrationProductId || ""}
            applicationData={applicationDataForPayment}
            initialData={{
              paymentMethod: formData.paymentMethod,
              paymentType: formData.paymentType,
              transactionId: formData.transactionId,
            }}
          />
        );
      default:
        return null;
    }
  };

  return (
    <ContentLayout title="Create New Application">
      <div className="flex justify-between items-center mb-6">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link href="/">Home</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link href="/applications">Applications</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Create New</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        <Button variant="outline" asChild>
          <Link href="/applications">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Applications
          </Link>
        </Button>
      </div>

      {/* Progress Indicator */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-2">
          <h2 className="text-lg font-semibold">
            Step {currentStep} of {steps.length}: {steps[currentStep - 1]?.name}
          </h2>
          <span className="text-sm text-muted-foreground">
            {Math.round(progress)}% Complete
          </span>
        </div>
        <Progress value={progress} className="w-full" />
        <p className="text-sm text-muted-foreground mt-1">
          {steps[currentStep - 1]?.description}
        </p>
      </div>

      {/* Current Step Form */}
      <div className="max-w-2xl mx-auto">{renderCurrentStep()}</div>

      {/* Loading Overlay */}
      {isSubmitting && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg">
            <div className="flex items-center space-x-3">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
              <span>Creating application...</span>
            </div>
          </div>
        </div>
      )}
    </ContentLayout>
  );
};

export default CreateApplicationPage;
