"use client";

import React, { useState, useEffect, useCallback, useRef } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import Link from "next/link";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ApplicationsDataTable } from "@/components/applications/applications-datatable";
import { useProfile } from "@/hooks/use-query";
import { Plus } from "lucide-react";

interface PaginationState {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

const ApplicationsPage: React.FC = () => {
  const { data: session, status } = useSession();
  const { data: profileData } = useProfile();
  const router = useRouter();

  const [applications, setApplications] = useState<IApplication[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState<PaginationState>({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  });

  // Filter states
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("");
  const [priorityLevelFilter, setPriorityLevelFilter] = useState<string>("");

  // Request management
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Fetch applications - memoized to prevent infinite re-renders
  const fetchApplications = useCallback(
    async (
      page: number = 1,
      currentFilters: {
        search?: string;
        status?: string;
        priorityLevel?: string;
      } = {}
    ) => {
      if (!session) {
        setLoading(false);
        return;
      }

      setLoading(true);
      try {
        // Build query parameters
        const params = new URLSearchParams({
          page: page.toString(),
          limit: "10", // Fixed limit to avoid dependency on pagination state
        });

        Object.entries(currentFilters).forEach(([key, value]) => {
          if (value) {
            if (key === "priorityLevel") {
              params.append("priority_level", value);
            } else {
              params.append(key, value);
            }
          }
        });

        // Use the Next.js API route instead of direct backend call
        const response = await fetch(`/api/applications?${params.toString()}`, {
          headers: {
            "Content-Type": "application/json",
          },
          cache: "no-store",
        });

        if (response.ok) {
          const result: IApplicationsResponse = await response.json();

          setApplications(result.data || []);
          setPagination({
            page: result.page || 1,
            limit: result.limit || 10,
            total: result.total || 0,
            totalPages: result.totalPages || 0,
          });
        } else {
          const errorData = await response.json().catch(() => ({}));
          console.error(
            "Failed to fetch applications, status:",
            response.status,
            errorData
          );
          setApplications([]);
          setPagination({
            page: 1,
            limit: 10,
            total: 0,
            totalPages: 0,
          });
        }
      } catch (error) {
        console.error("Error fetching applications:", error);
        setApplications([]);
        setPagination({
          page: 1,
          limit: 10,
          total: 0,
          totalPages: 0,
        });
      } finally {
        setLoading(false);
      }
    },
    [session]
  ); // Include session in dependency array

  // Initial load - handle authentication and fetch data
  useEffect(() => {
    if (status === "loading") {
      return; // Still loading session
    }

    if (status === "unauthenticated") {
      // Redirect to signin with return URL
      const returnUrl = encodeURIComponent("/applications");
      router.push(`/signin?callbackUrl=${returnUrl}`);
      return;
    }

    if (status === "authenticated" && session) {
      fetchApplications();
    }
  }, [fetchApplications, status, session, router]);

  // Cleanup function to cancel ongoing requests
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  // Handlers with proper debouncing and request management
  const handlePageChange = useCallback(
    (page: number) => {
      setPagination((prev) => ({ ...prev, page }));
      fetchApplications(page, {
        search: searchQuery,
        status: statusFilter,
        priorityLevel: priorityLevelFilter,
      });
    },
    [fetchApplications, searchQuery, statusFilter, priorityLevelFilter]
  );

  const handleSearch = useCallback(
    (search: string) => {
      setSearchQuery(search);

      // Clear existing timeout
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }

      // Debounce search requests by 400ms
      searchTimeoutRef.current = setTimeout(() => {
        fetchApplications(1, {
          search,
          status: statusFilter,
          priorityLevel: priorityLevelFilter,
        });
      }, 400);
    },
    [fetchApplications, statusFilter, priorityLevelFilter]
  );

  const handleStatusFilter = useCallback(
    (status: string) => {
      setStatusFilter(status);
      fetchApplications(1, {
        search: searchQuery,
        status,
        priorityLevel: priorityLevelFilter,
      });
    },
    [fetchApplications, searchQuery, priorityLevelFilter]
  );

  const handlePriorityLevelFilter = useCallback(
    (priorityLevel: string) => {
      setPriorityLevelFilter(priorityLevel);
      fetchApplications(1, {
        search: searchQuery,
        status: statusFilter,
        priorityLevel,
      });
    },
    [fetchApplications, searchQuery, statusFilter]
  );

  // Refresh function to reload current data
  const handleRefresh = useCallback(() => {
    fetchApplications(pagination.page, {
      search: searchQuery,
      status: statusFilter,
      priorityLevel: priorityLevelFilter,
    });
  }, [
    fetchApplications,
    pagination.page,
    searchQuery,
    statusFilter,
    priorityLevelFilter,
  ]);

  // Show loading state while session is loading
  if (status === "loading") {
    return (
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Applications</h1>
            <p className="text-muted-foreground">
              Manage and view all application records
            </p>
          </div>
        </div>
        <Card>
          <CardContent className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p>Loading session...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Applications</h1>
          <p className="text-muted-foreground">
            Manage and view all application records
          </p>
        </div>
        <Button asChild>
          <Link href="/applications/new">
            <Plus className="mr-2 h-4 w-4" />
            Create New Application
          </Link>
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Application Records</CardTitle>
        </CardHeader>
        <CardContent>
          <ApplicationsDataTable
            data={applications}
            loading={loading}
            pagination={pagination}
            onPageChange={handlePageChange}
            onSearch={handleSearch}
            onStatusFilter={handleStatusFilter}
            onPriorityLevelFilter={handlePriorityLevelFilter}
            onRefresh={handleRefresh}
            userRole={profileData?.data?.tokenType}
          />
        </CardContent>
      </Card>
    </div>
  );
};

export default ApplicationsPage;
