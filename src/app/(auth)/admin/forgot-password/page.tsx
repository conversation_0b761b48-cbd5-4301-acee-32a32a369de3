import React, { Suspense } from "react";
import Image from "next/image";
import AdminForgotPasswordForm from "@/components/auth/admin-forgot-password-form";

/**
 * Admin Forgot Password Page
 * Provides a form for admin users to request password reset
 * This page is accessible without authentication
 * @return {JSX.Element} The admin forgot password page component
 */
const AdminForgotPasswordPage = () => {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <div className="w-full lg:grid lg:min-h-[600px] lg:grid-cols-2 xl:min-h-[800px]">
        <div className="flex items-center justify-center py-12">
          <div className="mx-auto grid w-[400px] gap-6">
            <div className="grid gap-2 text-center">
              <h1 className="text-3xl font-bold">Admin Password Reset</h1>
              <p className="text-balance text-muted-foreground">
                Enter your admin email address to reset your password
              </p>
            </div>
            <AdminForgotPasswordForm />
          </div>
        </div>
        <div className="hidden bg-muted lg:block w-full h-full">
          <Image
            src="/auth/placeholder.svg"
            alt="Admin Password Reset"
            width="1920"
            height="1080"
            className="h-[100vh] w-full object-cover dark:brightness-[0.2] dark:grayscale"
          />
        </div>
      </div>
    </Suspense>
  );
};

export default AdminForgotPasswordPage;
