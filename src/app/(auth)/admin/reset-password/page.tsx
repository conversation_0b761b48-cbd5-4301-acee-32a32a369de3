import React, { Suspense } from "react";
import Image from "next/image";
import AdminResetPasswordForm from "@/components/auth/admin-reset-password-form";

/**
 * Admin Reset Password Page
 * Provides a form for admin users to reset their password using a valid reset token
 * This page is accessible without authentication but requires a valid token
 * @return {JSX.Element} The admin reset password page component
 */
const AdminResetPasswordPage = () => {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <div className="w-full lg:grid lg:min-h-[600px] lg:grid-cols-2 xl:min-h-[800px]">
        <div className="flex items-center justify-center py-12">
          <div className="mx-auto grid w-[400px] gap-6">
            <div className="grid gap-2 text-center">
              <h1 className="text-3xl font-bold">Reset Admin Password</h1>
              <p className="text-balance text-muted-foreground">
                Enter your new admin password below
              </p>
            </div>
            <AdminResetPasswordForm />
          </div>
        </div>
        <div className="hidden bg-muted lg:block w-full h-full">
          <Image
            src="/auth/placeholder.svg"
            alt="Admin Reset Password"
            width="1920"
            height="1080"
            className="h-[100vh] w-full object-cover dark:brightness-[0.2] dark:grayscale"
          />
        </div>
      </div>
    </Suspense>
  );
};

export default AdminResetPasswordPage;
