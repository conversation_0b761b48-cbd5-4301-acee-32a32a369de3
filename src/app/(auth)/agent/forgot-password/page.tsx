import React, { Suspense } from "react";
import Image from "next/image";
import AgentForgotPasswordForm from "@/components/auth/agent-forgot-password-form";

/**
 * Agent Forgot Password Page
 * Provides a form for agent users to request password reset
 * This page is accessible without authentication
 * @return {JSX.Element} The agent forgot password page component
 */
const AgentForgotPasswordPage = () => {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <div className="w-full lg:grid lg:min-h-[600px] lg:grid-cols-2 xl:min-h-[800px]">
        <div className="flex items-center justify-center py-12">
          <div className="mx-auto grid w-[400px] gap-6">
            <div className="grid gap-2 text-center">
              <h1 className="text-3xl font-bold">Agent Password Reset</h1>
              <p className="text-balance text-muted-foreground">
                Enter your agent email address to reset your password
              </p>
            </div>
            <AgentForgotPasswordForm />
          </div>
        </div>
        <div className="hidden bg-muted lg:block w-full h-full">
          <Image
            src="/auth/placeholder.svg"
            alt="Agent Password Reset"
            width="1920"
            height="1080"
            className="h-[100vh] w-full object-cover dark:brightness-[0.2] dark:grayscale"
          />
        </div>
      </div>
    </Suspense>
  );
};

export default AgentForgotPasswordPage;
