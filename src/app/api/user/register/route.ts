import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { apiUrl } from "@/utils/urls";

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.backendTokens?.accessToken) {
      return NextResponse.json(
        { success: false, message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();

    // Validate required fields
    if (!body.name || !body.email || !body.mobileNo) {
      return NextResponse.json(
        {
          success: false,
          message: "Name, email, and mobile number are required",
        },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(body.email)) {
      return NextResponse.json(
        {
          success: false,
          message: "Invalid email format",
        },
        { status: 400 }
      );
    }

    // Create user in backend using the register endpoint
    const backendUrl = `${apiUrl}/user/register`;

    const response = await fetch(backendUrl, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${session.backendTokens.accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        name: body.name,
        email: body.email,
        emailVerified: true, // Set to true for password-less registration
        mobileNo: body.mobileNo,
      }),
    });

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(
        {
          success: false,
          message: data.message || "Failed to register user",
        },
        { status: response.status }
      );
    }

    // Ensure we have user data with an ID
    const userData = data.user || data;
    if (!userData || !userData.id) {
      return NextResponse.json(
        {
          success: false,
          message:
            "User registration succeeded but invalid user data was returned",
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: data.message || "User registered successfully",
      data: userData,
    });
  } catch (error) {
    console.error("Error registering user:", error);
    return NextResponse.json(
      { success: false, message: "Internal server error" },
      { status: 500 }
    );
  }
}
