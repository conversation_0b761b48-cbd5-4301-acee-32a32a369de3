import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { apiUrl } from "@/utils/urls";

/**
 * DELETE /api/applications/[id]/documents/[documentId]
 * Deletes a specific document from an application
 *
 * @param {NextRequest} request - The incoming request object
 * @param {Object} params - Route parameters containing application and document IDs
 * @param {string} params.id - The application ID
 * @param {string} params.documentId - The document ID to delete
 * @return {Promise<NextResponse>} Response with deletion status and data
 *
 * Expected backend response format:
 * {
 *   "status": "success",
 *   "message": "Document deleted successfully",
 *   "data": {
 *     "documentId": "cmdyyaiej0007zp8v0iotdi6q",
 *     "applicationId": "cmdyyaid60003zp8vjxzkcv4i",
 *     "deletedFiles": [
 *       "careerireland/documents/cmdyyaid60003zp8vjxzkcv4i/data_management_masterclass_presentation_1754488257896_kboph9.pdf"
 *     ]
 *   }
 * }
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string; documentId: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    // Check authentication
    if (!session?.backendTokens?.accessToken) {
      return NextResponse.json(
        { success: false, message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id, documentId } = params;

    // Validate required parameters
    if (!id || !documentId) {
      return NextResponse.json(
        {
          success: false,
          message: "Application ID and Document ID are required",
        },
        { status: 400 }
      );
    }

    // Delete document from backend
    const backendUrl = `${apiUrl}/applications/${id}/documents/${documentId}`;

    const response = await fetch(backendUrl, {
      method: "DELETE",
      headers: {
        Authorization: `Bearer ${session.backendTokens.accessToken}`,
        "Content-Type": "application/json",
      },
    });

    const data = await response.json();

    if (response.ok) {
      return NextResponse.json({
        success: true,
        message: data.message || "Document deleted successfully",
        data: data.data || {
          documentId,
          applicationId: id,
          deletedFiles: [],
        },
      });
    } else {
      console.error("Backend document deletion failed:", {
        status: response.status,
        statusText: response.statusText,
        data,
      });

      return NextResponse.json(
        {
          success: false,
          message: data.message || "Failed to delete document",
        },
        { status: response.status }
      );
    }
  } catch (error) {
    console.error("Document deletion error:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Internal server error",
      },
      { status: 500 }
    );
  }
}
