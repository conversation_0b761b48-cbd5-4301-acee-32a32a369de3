import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { apiUrl } from "@/utils/urls";

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.backendTokens?.accessToken) {
      return NextResponse.json(
        { success: false, message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id } = params;

    if (!id) {
      return NextResponse.json(
        { success: false, message: "Application ID is required" },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { note } = body;

    if (note === undefined || note === null) {
      return NextResponse.json(
        { success: false, message: "Note field is required" },
        { status: 400 }
      );
    }

    // Validate note length (optional - adjust as needed)
    if (typeof note !== "string") {
      return NextResponse.json(
        {
          success: false,
          message: "Note must be a string.",
        },
        { status: 400 }
      );
    }

    // Update note in backend
    const backendUrl = `${apiUrl}/applications/${id}/note`;

    const response = await fetch(backendUrl, {
      method: "PUT",
      headers: {
        Authorization: `Bearer ${session.backendTokens.accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ note }),
    });

    const data = await response.json();

    if (response.ok) {
      return NextResponse.json({
        success: true,
        message: "Note updated successfully",
        data,
      });
    } else {
      console.error("Backend note update failed:", {
        status: response.status,
        statusText: response.statusText,
        data,
      });

      return NextResponse.json(
        {
          success: false,
          message: data.message || `Failed to update note (${response.status})`,
        },
        { status: response.status }
      );
    }
  } catch (error) {
    console.error("Error updating note:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Internal server error while updating note",
      },
      { status: 500 }
    );
  }
}
