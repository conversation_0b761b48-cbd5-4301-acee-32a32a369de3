import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { apiUrl } from "@/utils/urls";

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.backendTokens?.accessToken) {
      return NextResponse.json(
        { success: false, message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id } = params;

    if (!id) {
      return NextResponse.json(
        { success: false, message: "Application ID is required" },
        { status: 400 }
      );
    }

    // Parse the FormData from the request
    const formData = await request.formData();

    const file = formData.get("file") as File;
    const documentName = formData.get("document_name") as string;
    const stageOrder = formData.get("stage_order") as string;
    const documentId = formData.get("document_id") as string;

    if (!file || !documentName || !stageOrder) {
      return NextResponse.json(
        {
          success: false,
          message: "File, document_name, and stage_order are required",
        },
        { status: 400 }
      );
    }

    // Validate document_id if provided
    if (documentId && typeof documentId !== "string") {
      return NextResponse.json(
        {
          success: false,
          message: "document_id must be a string",
        },
        { status: 400 }
      );
    }

    // Create FormData for backend request
    const backendFormData = new FormData();
    backendFormData.append("file", file);
    backendFormData.append("document_name", documentName);
    backendFormData.append("stage_order", stageOrder);
    if (documentId) {
      backendFormData.append("document_id", documentId);
    }

    // Upload document to backend
    const backendUrl = `${apiUrl}/applications/${id}/document`;

    const response = await fetch(backendUrl, {
      method: "PUT",
      headers: {
        Authorization: `Bearer ${session.backendTokens.accessToken}`,
        // Don't set Content-Type for FormData - let the browser set it with boundary
      },
      body: backendFormData,
    });

    const data = await response.json();

    if (response.ok) {
      return NextResponse.json(data);
    } else {
      return NextResponse.json(
        {
          success: false,
          message: data.message || "Failed to upload document",
        },
        { status: response.status }
      );
    }
  } catch (error) {
    console.error("Error uploading document:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Internal server error",
      },
      { status: 500 }
    );
  }
}
