import { NextRequest, NextResponse } from "next/server";
import { Zod<PERSON>rror } from "zod";
import {
  adminForgotPasswordSchema,
  adminResetPasswordSchema,
} from "@/utils/schema";
import { apiUrl } from "@/utils/urls";

/**
 * POST /api/admin/reset-password
 * Handles both forgot password (email only) and reset password (newPassword + token) for admin users
 *
 * @param {NextRequest} request - The incoming request object
 *
 * For forgot password request body:
 * {
 *   "email": "<EMAIL>"
 * }
 *
 * For reset password request body:
 * {
 *   "newPassword": "newPassword123",
 *   "token": "reset_token_string"
 * }
 *
 * @return {Promise<NextResponse>} The response object
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Check if this is a forgot password request (email only) or reset password request (token + newPassword)
    if (body.email && !body.token && !body.newPassword) {
      // This is a forgot password request
      return handleForgotPassword(body);
    } else if (body.token && body.newPassword) {
      // This is a reset password request
      return handleResetPassword(body);
    } else {
      return NextResponse.json(
        {
          success: false,
          message:
            "Invalid request. Provide either email for forgot password or token + newPassword for reset password.",
        },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error("Admin reset password API error:", error);

    // Handle JSON parsing errors
    if (error instanceof SyntaxError) {
      return NextResponse.json(
        {
          success: false,
          message: "Invalid JSON in request body",
        },
        { status: 400 }
      );
    }

    // Handle unexpected errors
    return NextResponse.json(
      {
        success: false,
        message: "An unexpected error occurred while processing your request",
      },
      { status: 500 }
    );
  }
}

/**
 * Handles forgot password requests for admin users
 * @param {any} body - Request body containing email
 * @return {Promise<NextResponse>} Response object
 */
async function handleForgotPassword(body: any): Promise<NextResponse> {
  try {
    // Validate request body using Zod schema
    const validatedData = adminForgotPasswordSchema.parse(body);
    const { email } = validatedData;

    // Send request to backend
    const response = await fetch(`${apiUrl}/admin/reset-password`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ email }),
    });

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(
        {
          success: false,
          message: data.message || "Failed to send password reset email",
        },
        { status: response.status }
      );
    }

    return NextResponse.json({
      success: true,
      message:
        "Password reset instructions have been sent to your email address",
      email,
    });
  } catch (error) {
    console.error("Admin forgot password error:", error);

    // Handle Zod validation errors
    if (error instanceof ZodError) {
      const errorMessages = error.errors.map((err) => err.message).join(", ");
      return NextResponse.json(
        {
          success: false,
          message: `Validation error: ${errorMessages}`,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        message: "Failed to process forgot password request",
      },
      { status: 500 }
    );
  }
}

/**
 * Handles reset password requests for admin users
 * @param {any} body - Request body containing token and newPassword
 * @return {Promise<NextResponse>} Response object
 */
async function handleResetPassword(body: any): Promise<NextResponse> {
  try {
    // Validate request body using Zod schema
    const validatedData = adminResetPasswordSchema.parse(body);
    const { token, newPassword } = validatedData;

    // Send request to backend
    const response = await fetch(`${apiUrl}/admin/reset-password/confirm`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ token, newPassword }),
    });

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(
        {
          success: false,
          message: data.message || "Failed to reset password",
        },
        { status: response.status }
      );
    }

    return NextResponse.json({
      success: true,
      message:
        "Your password has been successfully reset. You can now sign in with your new password.",
    });
  } catch (error) {
    console.error("Admin reset password error:", error);

    // Handle Zod validation errors
    if (error instanceof ZodError) {
      const errorMessages = error.errors.map((err) => err.message).join(", ");
      return NextResponse.json(
        {
          success: false,
          message: `Validation error: ${errorMessages}`,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        message: "Failed to reset password",
      },
      { status: 500 }
    );
  }
}
