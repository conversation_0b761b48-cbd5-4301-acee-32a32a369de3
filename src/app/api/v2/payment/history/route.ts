import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { apiUrl } from "@/utils/urls";

// Force dynamic rendering to prevent static generation errors when using headers()
// This is required because getServerSession() uses headers() internally
export const dynamic = "force-dynamic";

/**
 * GET /api/v2/payment/history
 *
 * Retrieves payment history with pagination and filtering support.
 * Forwards all query parameters to the backend API.
 *
 * @param {NextRequest} request Next.js request object containing query parameters
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate user session
    const session = await getServerSession(authOptions);

    if (!session?.backendTokens?.accessToken) {
      return NextResponse.json(
        {
          success: false,
          message:
            "Authentication required. Please log in to access payment history.",
        },
        { status: 401 }
      );
    }

    // Extract and forward all query parameters to the backend
    const { searchParams } = new URL(request.url);
    const backendUrl = `${apiUrl}/v2/payment/history?${searchParams.toString()}`;

    // Make authenticated request to backend API
    const response = await fetch(backendUrl, {
      headers: {
        Authorization: `Bearer ${session.backendTokens.accessToken}`,
        "Content-Type": "application/json",
      },
      cache: "no-store", // Ensure fresh data for payment history
    });

    const data = await response.json();

    if (response.ok) {
      return NextResponse.json(data);
    } else {
      // Handle backend errors with user-friendly messages
      const errorMessage =
        data.message ||
        "Unable to retrieve payment history. Please try again later.";
      return NextResponse.json(
        {
          success: false,
          message: errorMessage,
        },
        { status: response.status }
      );
    }
  } catch (error) {
    // Log error for debugging while providing user-friendly message
    console.error("Error fetching payment history:", error);
    return NextResponse.json(
      {
        success: false,
        message:
          "A technical error occurred while retrieving payment history. Please try again or contact support if the issue persists.",
      },
      { status: 500 }
    );
  }
}
