import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { apiUrl } from "@/utils/urls";

export async function PATCH(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.backendTokens?.accessToken) {
      return NextResponse.json(
        { success: false, message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();

    // Validate required fields
    if (!body.paymentId || !body.progress) {
      return NextResponse.json(
        {
          success: false,
          message: "Payment ID and progress are required",
        },
        { status: 400 }
      );
    }

    // Validate progress value - Standardized status parameters
    const validProgressValues = [
      "Accepted",
      "Rejected",
      "Pending",
      "Completed",
      "Active",
      "Inactive",
      "Blocked",
      "Cancelled",
    ];
    if (!validProgressValues.includes(body.progress)) {
      return NextResponse.json(
        {
          success: false,
          message: `Invalid progress value ${body.progress}`,
        },
        { status: 400 }
      );
    }

    const response = await fetch(`${apiUrl}/v2/payment/progress`, {
      method: "PATCH",
      headers: {
        Authorization: `Bearer ${session.backendTokens.accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    });

    const data = await response.json();

    if (response.ok) {
      return NextResponse.json(data);
    } else {
      return NextResponse.json(
        {
          success: false,
          message: data.message || "Failed to update payment progress",
        },
        { status: response.status }
      );
    }
  } catch (error) {
    console.error("Error updating payment progress:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Internal server error",
      },
      { status: 500 }
    );
  }
}
