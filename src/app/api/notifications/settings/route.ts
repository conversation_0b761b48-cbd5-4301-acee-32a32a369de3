import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { apiUrl } from "@/utils/urls";
import { notificationSettingsSchema } from "@/utils/schema";

// Force dynamic rendering for session handling
export const dynamic = "force-dynamic";

/**
 * GET /api/notifications/settings
 *
 * Retrieves notification settings for the authenticated user.
 * Calls backend API endpoint GET /notifications/settings.
 *
 * @param {NextRequest} _request Next.js request object (unused)
 * @return {Promise<NextResponse>} JSON response with notification settings
 */
export async function GET(_request: NextRequest) {
  try {
    // Authenticate user session
    const session = await getServerSession(authOptions);

    if (!session?.backendTokens?.accessToken) {
      return NextResponse.json(
        {
          success: false,
          message:
            "Authentication required. Please log in to access notification settings.",
        },
        { status: 401 }
      );
    }

    // Call backend API with timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    try {
      const response = await fetch(`${apiUrl}/notifications/settings`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session.backendTokens.accessToken}`,
        },
        cache: "no-store",
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        const data = await response.json();
        return NextResponse.json({
          success: true,
          data: data,
          message: "Notification settings retrieved successfully",
        });
      } else {
        const errorData = await response.json().catch(() => ({}));
        return NextResponse.json(
          {
            success: false,
            message:
              errorData.message || "Failed to retrieve notification settings",
          },
          { status: response.status }
        );
      }
    } catch (fetchError: any) {
      clearTimeout(timeoutId);

      if (fetchError.name === "AbortError") {
        return NextResponse.json(
          {
            success: false,
            message: "Request timed out. Please try again.",
          },
          { status: 408 }
        );
      }

      throw fetchError;
    }
  } catch (error) {
    console.error("Error in GET /api/notifications/settings:", error);
    return NextResponse.json(
      {
        success: false,
        message:
          "An error occurred while retrieving notification settings. Please try again.",
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/notifications/settings
 *
 * Updates notification settings for the authenticated user.
 * Calls backend API endpoint PUT /notifications/settings.
 *
 * @param {NextRequest} request Next.js request object containing settings data
 * @return {Promise<NextResponse>} JSON response with update result
 */
export async function PUT(request: NextRequest) {
  try {
    // Authenticate user session
    const session = await getServerSession(authOptions);

    if (!session?.backendTokens?.accessToken) {
      return NextResponse.json(
        {
          success: false,
          message:
            "Authentication required. Please log in to update notification settings.",
        },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();

    // Validate request body structure
    const validationResult = notificationSettingsSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          message: "Invalid notification settings data",
          errors: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    // Call backend API with timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    try {
      const response = await fetch(`${apiUrl}/notifications/settings`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${session.backendTokens.accessToken}`,
        },
        body: JSON.stringify(validationResult.data),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        const data = await response.json();
        return NextResponse.json({
          success: true,
          data: data,
          message: "Notification settings updated successfully",
        });
      } else {
        const errorData = await response.json().catch(() => ({}));
        return NextResponse.json(
          {
            success: false,
            message:
              errorData.message || "Failed to update notification settings",
          },
          { status: response.status }
        );
      }
    } catch (fetchError: any) {
      clearTimeout(timeoutId);

      if (fetchError.name === "AbortError") {
        return NextResponse.json(
          {
            success: false,
            message: "Request timed out. Please try again.",
          },
          { status: 408 }
        );
      }

      throw fetchError;
    }
  } catch (error) {
    console.error("Error in PUT /api/notifications/settings:", error);
    return NextResponse.json(
      {
        success: false,
        message:
          "An error occurred while updating notification settings. Please try again.",
      },
      { status: 500 }
    );
  }
}
