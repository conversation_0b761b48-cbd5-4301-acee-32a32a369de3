/* eslint-disable new-cap */

import { apiUrl } from "@/utils/urls";
import { NextAuthOptions } from "next-auth";
import { JWT } from "next-auth/jwt";
import NextAuth from "next-auth/next";
import Credentials<PERSON>rovider from "next-auth/providers/credentials";

async function refreshToken(token: JWT): Promise<JWT> {
  try {
    // Determine the correct refresh endpoint based on provider
    const refreshEndpoint =
      token.provider === "agent-credentials"
        ? "/agents/refresh"
        : "/admin/refresh";

    const res = await fetch(apiUrl + refreshEndpoint, {
      method: "POST",
      headers: {
        authorization: `Bearer ${token.backendTokens.refreshToken}`,
      },
    });

    if (!res.ok) {
      throw new Error(`Failed to refresh token: ${res.status}`);
    }

    const response = await res.json();

    return {
      ...token,
      backendTokens: response,
      tokenCreatedAt: Date.now(), // Update creation time for refreshed token
    };
  } catch (error) {
    throw new Error("Token refresh failed");
  }
}

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      id: "credentials",
      name: "Admin Credentials",
      credentials: {
        email: {
          label: "Email",
          type: "text",
          placeholder: "<EMAIL>",
        },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials, req) {
        if (!credentials?.email || !credentials?.password) {
          return {
            error: true,
            message: "Please enter both email and password",
          };
        }

        const { email, password } = credentials;

        try {
          const res = await fetch(apiUrl + "/admin/login", {
            method: "POST",
            body: JSON.stringify({
              email,
              password,
            }),
            headers: {
              "Content-Type": "application/json",
            },
          });

          const user = await res.json();

          if (!res.ok) {
            return {
              error: true,
              message:
                "Invalid email or password. Please check your credentials and try again.",
            };
          }

          return user;
        } catch (error) {
          console.error("Admin login error:", error);
          return {
            error: true,
            message:
              "Login failed. Please check your connection and try again.",
          };
        }
      },
    }),
    CredentialsProvider({
      id: "agent-credentials",
      name: "Agent Credentials",
      credentials: {
        email: {
          label: "Email",
          type: "text",
          placeholder: "<EMAIL>",
        },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials, req) {
        if (!credentials?.email || !credentials?.password) {
          return {
            error: true,
            message: "Please enter both email and password",
          };
        }

        const { email, password } = credentials;

        try {
          const res = await fetch(apiUrl + "/agents/login", {
            method: "POST",
            body: JSON.stringify({
              email,
              password,
            }),
            headers: {
              "Content-Type": "application/json",
            },
          });

          if (!res.ok) {
            return {
              error: true,
              message:
                "Invalid email or password. Please check your credentials and try again.",
            };
          }

          const data = await res.json();

          // Backend returns: { user: {...}, backendTokens: {...} }
          // NextAuth expects this exact structure
          return data;
        } catch (error) {
          console.error("Agent login error:", error);
          return {
            error: true,
            message:
              "Login failed. Please check your connection and try again.",
          };
        }
      },
    }),
  ],
  // Session configuration for 10-hour duration
  session: {
    strategy: "jwt",
    maxAge: 10 * 60 * 60, // 10 hours in seconds (36000 seconds)
    updateAge: 60 * 60, // Update session every hour (3600 seconds)
  },
  // JWT configuration for 10-hour duration
  jwt: {
    maxAge: 10 * 60 * 60, // 10 hours in seconds (36000 seconds)
  },
  pages: {
    signIn: "/signin",
    error: "/signin",
  },
  callbacks: {
    async signIn({ user, account }) {
      // @ts-ignore
      const { error, message } = user;
      if (!error) return true;

      // Include provider information in redirect URL for correct tab selection
      switch (account?.provider) {
        case "google":
          return `/signin?error=${encodeURIComponent(message)}&provider=google`;
        case "credentials":
          // Admin login failure - redirect to admin tab
          return `/signin?error=${encodeURIComponent(message)}&provider=admin`;
        case "agent-credentials":
          // Agent login failure - redirect to agent tab
          return `/signin?error=${encodeURIComponent(message)}&provider=agent`;
        default:
          return true;
      }
    },
    async jwt({ token, user, account }) {
      if (user) {
        // Store the provider information and token creation time
        return {
          ...token,
          ...user,
          provider: account?.provider, // Store which provider was used
          tokenCreatedAt: Date.now(), // Store when token was created
        };
      }

      // Check if token exists and has required properties
      if (!token.backendTokens?.expiresIn || !token.tokenCreatedAt) {
        // If missing required token data, trigger refresh
        try {
          return await refreshToken(token);
        } catch (error) {
          return {
            ...token,
            error: "RefreshAccessTokenError",
          };
        }
      }

      // Calculate token expiry time: creation time + expiry duration (in milliseconds)
      const tokenExpiryTime =
        token.tokenCreatedAt + token.backendTokens.expiresIn;
      const currentTime = Date.now();

      // If token is still valid (not expired), return it
      if (currentTime < tokenExpiryTime) {
        return token;
      }

      // Token has expired, attempt to refresh
      try {
        const refreshedToken = await refreshToken(token);
        // Update the token creation time for the refreshed token
        return {
          ...refreshedToken,
          tokenCreatedAt: Date.now(),
        };
      } catch (error) {
        // If refresh fails, return a token with expired flag to trigger logout
        return {
          ...token,
          error: "RefreshAccessTokenError",
        };
      }
    },

    async session({ token, session }) {
      // If token has error, return session with error flag
      if (token.error) {
        return {
          ...session,
          error: "RefreshAccessTokenError",
        };
      }

      // Fix for agent sessions where token.user is undefined
      // Extract user data from access token if needed
      let userData = token.user;
      if (
        !userData &&
        token.provider === "agent-credentials" &&
        token.backendTokens?.accessToken
      ) {
        try {
          const tokenPayload = JSON.parse(
            atob(token.backendTokens.accessToken.split(".")[1])
          );

          userData = {
            id: tokenPayload.id,
            email: tokenPayload.email,
            name: tokenPayload.sub?.name || "Agent User",
          };
        } catch (error) {
          console.error("Error extracting user data from token:", error);
        }
      }

      session.user = userData;
      session.backendTokens = {
        ...token.backendTokens,
        provider: token.provider as string, // Include provider info in session
      } as any;

      return session;
    },
  },
};

const handler = NextAuth(authOptions);

export { handler as GET, handler as POST };
