import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { apiUrl } from "@/utils/urls";

/**
 * PATCH /api/immigration/[id]
 * Updates an immigration service record
 * Used by the modal-based edit functionality
 * @param {NextRequest} request - The incoming request with immigration data
 * @param {Object} params - Route parameters
 * @param {string} params.id - The immigration service ID
 * @return {Promise<NextResponse>} JSON response with success/error status
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.backendTokens?.accessToken) {
      return NextResponse.json(
        { success: false, message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id } = params;

    if (!id) {
      return NextResponse.json(
        { success: false, message: "Immigration service ID is required" },
        { status: 400 }
      );
    }

    const body = await request.json();

    // Update immigration service in backend
    const backendUrl = `${apiUrl}/immigration/${id}`;

    const response = await fetch(backendUrl, {
      method: "PATCH",
      headers: {
        Authorization: `Bearer ${session.backendTokens.accessToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    });

    const data = await response.json();

    if (response.ok) {
      return NextResponse.json(data);
    } else {
      return NextResponse.json(
        {
          success: false,
          message: data.message || "Failed to update immigration service",
        },
        { status: response.status }
      );
    }
  } catch (error) {
    console.error("Error updating immigration service:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Internal server error",
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/immigration/[id]
 * Deletes an immigration service record
 * Handles 409 ConflictException responses when deletion is not possible
 * @param {NextRequest} request - The incoming request
 * @param {Object} params - Route parameters
 * @param {string} params.id - The immigration service ID
 * @return {Promise<NextResponse>} JSON response with success/error status
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.backendTokens?.accessToken) {
      return NextResponse.json(
        { success: false, message: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id } = params;

    if (!id) {
      return NextResponse.json(
        { success: false, message: "Immigration service ID is required" },
        { status: 400 }
      );
    }

    // Delete immigration service in backend
    const backendUrl = `${apiUrl}/immigration/${id}`;

    const response = await fetch(backendUrl, {
      method: "DELETE",
      headers: {
        Authorization: `Bearer ${session.backendTokens.accessToken}`,
        "Content-Type": "application/json",
      },
    });

    const data = await response.json();

    if (response.ok) {
      return NextResponse.json(data);
    } else {
      // Forward the exact error response from backend, including 409 conflicts
      return NextResponse.json(data, { status: response.status });
    }
  } catch (error) {
    console.error("Error deleting immigration service:", error);
    return NextResponse.json(
      {
        success: false,
        message: "Internal server error",
      },
      { status: 500 }
    );
  }
}
