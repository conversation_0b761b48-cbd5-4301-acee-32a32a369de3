import { clientFetch<PERSON><PERSON> } from "@/lib/client-fetch";

/**
 * Password Reset Service Layer
 * Handles password reset operations for both admin and agent users:
 * - POST /api/admin/reset-password (email for forgot, token + newPassword for reset)
 * - POST /api/agents/reset-password (email for forgot, token + newPassword for reset)
 */

/**
 * Initiates a forgot password request for admin users
 * @param {string} email - Admin user's email address
 * @return {Promise<any>} Response from the API
 */
export async function adminForgotPassword(email: string): Promise<any> {
  try {
    const response = await clientFetchJson("/api/admin/reset-password", {
      method: "POST",
      body: JSON.stringify({ email }),
    });

    return response;
  } catch (error) {
    console.error("Admin forgot password error:", error);
    throw new Error("Failed to send password reset email");
  }
}

/**
 * Resets password for admin users
 * @param {string} token - Reset token from email
 * @param {string} newPassword - New password
 * @param {string} confirmPassword - Password confirmation
 * @return {Promise<any>} Response from the API
 */
export async function adminResetPassword(
  token: string,
  newPassword: string,
  confirmPassword: string
): Promise<any> {
  try {
    const response = await clientFetchJson("/api/admin/reset-password", {
      method: "POST",
      body: JSON.stringify({ token, newPassword, confirmPassword }),
    });

    return response;
  } catch (error) {
    console.error("Admin reset password error:", error);
    throw new Error("Failed to reset password");
  }
}

/**
 * Initiates a forgot password request for agent users
 * @param {string} email - Agent user's email address
 * @return {Promise<any>} Response from the API
 */
export async function agentForgotPassword(email: string): Promise<any> {
  try {
    const response = await clientFetchJson("/api/agents/reset-password", {
      method: "POST",
      body: JSON.stringify({ email }),
    });

    return response;
  } catch (error) {
    console.error("Agent forgot password error:", error);
    throw new Error("Failed to send password reset email");
  }
}

/**
 * Resets password for agent users
 * @param {string} token - Reset token from email
 * @param {string} newPassword - New password
 * @param {string} confirmPassword - Password confirmation
 * @return {Promise<any>} Response from the API
 */
export async function agentResetPassword(
  token: string,
  newPassword: string,
  confirmPassword: string
): Promise<any> {
  try {
    const response = await clientFetchJson("/api/agents/reset-password", {
      method: "POST",
      body: JSON.stringify({ token, newPassword, confirmPassword }),
    });

    return response;
  } catch (error) {
    console.error("Agent reset password error:", error);
    throw new Error("Failed to reset password");
  }
}

/**
 * Validates if a token is present in the URL query parameters
 * @param {URLSearchParams} searchParams - URL search parameters
 * @return {string | null} Token if present, null otherwise
 */
export function getTokenFromUrl(searchParams: URLSearchParams): string | null {
  return searchParams.get("token");
}

/**
 * Validates password strength for both admin and agent users
 * @param {string} password - Password to validate
 * @return {object} Validation result
 */
export function validatePasswordStrength(password: string): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (password.length < 8) {
    errors.push("Password must be at least 8 characters long");
  }

  if (password.length > 50) {
    errors.push("Password must be less than 50 characters long");
  }

  if (!/[a-z]/.test(password)) {
    errors.push("Password must contain at least one lowercase letter");
  }

  if (!/[A-Z]/.test(password)) {
    errors.push("Password must contain at least one uppercase letter");
  }

  if (!/\d/.test(password)) {
    errors.push("Password must contain at least one number");
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Generates user-friendly error messages
 * @param {string} operation - The operation being performed
 * @param {string} error - The error that occurred
 * @return {string} User-friendly error message
 */
export function getErrorMessage(
  operation: "forgot" | "reset",
  error: string
): string {
  const errorMessages: Record<string, Record<string, string>> = {
    forgot: {
      user_not_found: "No account found with this email address",
      invalid_email: "Please enter a valid email address",
      rate_limit: "Too many password reset requests. Please try again later",
      server_error:
        "Unable to process password reset request. Please try again",
    },
    reset: {
      invalid_token: "Invalid or expired reset link. Please request a new one",
      token_expired: "Reset link has expired. Please request a new one",
      weak_password: "Password does not meet security requirements",
      server_error: "Unable to reset password. Please try again",
    },
  };

  return errorMessages[operation]?.[error] || "An unexpected error occurred";
}
