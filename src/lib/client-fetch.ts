import { signOut } from "next-auth/react";
import { toast } from "sonner";

// Flag to prevent multiple logout calls
let isLoggingOut = false;

/**
 * Client-side fetch wrapper with automatic 401 handling
 * @param {string} url - The URL to fetch
 * @param {RequestInit} options - Fetch options
 * @return {Promise<Response>} Promise with the fetch response
 */
export async function clientFetch(
  url: string,
  options: RequestInit = {}
): Promise<Response> {
  try {
    const response = await fetch(url, {
      ...options,
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
    });

    // Handle 401 responses
    if (response.status === 401 && !isLoggingOut) {
      isLoggingOut = true;

      try {
        // Show user-friendly message
        toast.error("Your session has expired. Please log in again.", {
          duration: 3000,
        });

        // Sign out user and redirect to login
        await signOut({
          callbackUrl: "/signin",
          redirect: true,
        });
      } catch (signOutError) {
        console.error("Error during logout:", signOutError);
        // Fallback: redirect manually
        window.location.href = "/signin";
      } finally {
        isLoggingOut = false;
      }
    }

    return response;
  } catch (error) {
    console.error("Fetch error:", error);
    throw error;
  }
}

/**
 * Client-side fetch wrapper with JSON parsing and 401 handling
 * @param {string} url - The URL to fetch
 * @param {RequestInit} options - Fetch options
 * @return {Promise<T>} Promise with parsed JSON data
 */
export async function clientFetchJson<T = any>(
  url: string,
  options: RequestInit = {}
): Promise<T> {
  const response = await clientFetch(url, options);

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.message || `HTTP ${response.status}: ${response.statusText}`
    );
  }

  return response.json();
}
