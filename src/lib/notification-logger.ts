/**
 * Notification Logger Utility
 *
 * Provides structured logging for notification-related operations
 * with different log levels and error categorization.
 */

export interface LogEntry {
  timestamp: string;
  level: "info" | "warn" | "error" | "debug";
  category: "notification" | "api" | "component" | "hook";
  operation: string;
  message: string;
  data?: any;
  error?: {
    name: string;
    message: string;
    stack?: string;
  };
  userId?: string;
  applicationId?: string;
}

/**
 * Creates a structured log entry
 * @param {Partial<LogEntry>} entry - Log entry data
 * @return {LogEntry} Complete log entry with timestamp
 */
const createLogEntry = (entry: Partial<LogEntry>): LogEntry => {
  return {
    timestamp: new Date().toISOString(),
    level: entry.level || "info",
    category: entry.category || "notification",
    operation: entry.operation || "unknown",
    message: entry.message || "",
    ...entry,
  };
};

/**
 * Logs to console with structured format
 * In production, this could be extended to send logs to external services
 * @param {LogEntry} entry - Log entry to output
 */
const logToConsole = (entry: LogEntry): void => {
  const logMessage = `[${entry.timestamp}] ${entry.level.toUpperCase()} [${entry.category}:${entry.operation}] ${entry.message}`;

  switch (entry.level) {
    case "error":
      // eslint-disable-next-line no-console
      console.error(logMessage, entry.data, entry.error);
      break;
    case "warn":
      // eslint-disable-next-line no-console
      console.warn(logMessage, entry.data);
      break;
    case "debug":
      // eslint-disable-next-line no-console
      console.debug(logMessage, entry.data);
      break;
    default:
      // eslint-disable-next-line no-console
      console.log(logMessage, entry.data);
  }
};

/**
 * Notification Logger Class
 * Provides methods for logging notification-related operations
 */
export class NotificationLogger {
  /**
   * Logs successful notification fetch
   * @param {string} applicationId - Application ID
   * @param {any} data - Notification data received
   */
  static logNotificationFetch(applicationId: string, data: any): void {
    const entry = createLogEntry({
      level: "info",
      category: "notification",
      operation: "fetch_pending",
      message: `Successfully fetched pending notifications for application ${applicationId}`,
      data: { applicationId, hasNotification: !!data },
      applicationId,
    });
    logToConsole(entry);
  }

  /**
   * Logs notification fetch error
   * @param {string} applicationId - Application ID
   * @param {Error} error - Error that occurred
   */
  static logNotificationFetchError(applicationId: string, error: Error): void {
    const entry = createLogEntry({
      level: "error",
      category: "notification",
      operation: "fetch_pending_error",
      message: `Failed to fetch pending notifications for application ${applicationId}`,
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
      },
      applicationId,
    });
    logToConsole(entry);
  }

  /**
   * Logs successful notification send
   * @param {string} queueId - Notification queue ID
   * @param {string} applicationId - Application ID
   * @param {any} response - Send response data
   */
  static logNotificationSend(
    queueId: string,
    applicationId: string,
    response: any
  ): void {
    const entry = createLogEntry({
      level: "info",
      category: "notification",
      operation: "send_email",
      message: `Successfully sent notification email for queue ${queueId}`,
      data: { queueId, applicationId, sentAt: response.sentAt },
      applicationId,
    });
    logToConsole(entry);
  }

  /**
   * Logs notification send error
   * @param {string} queueId - Notification queue ID
   * @param {string} applicationId - Application ID
   * @param {Error} error - Error that occurred
   */
  static logNotificationSendError(
    queueId: string,
    applicationId: string,
    error: Error
  ): void {
    const entry = createLogEntry({
      level: "error",
      category: "notification",
      operation: "send_email_error",
      message: `Failed to send notification email for queue ${queueId}`,
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
      },
      data: { queueId, applicationId },
      applicationId,
    });
    logToConsole(entry);
  }

  /**
   * Logs API request errors
   * @param {string} endpoint - API endpoint
   * @param {string} method - HTTP method
   * @param {Error} error - Error that occurred
   * @param {any} requestData - Request data (optional)
   */
  static logApiError(
    endpoint: string,
    method: string,
    error: Error,
    requestData?: any
  ): void {
    const entry = createLogEntry({
      level: "error",
      category: "api",
      operation: `${method.toLowerCase()}_${endpoint.replace(/[^a-zA-Z0-9]/g, "_")}`,
      message: `API request failed: ${method} ${endpoint}`,
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
      },
      data: { endpoint, method, requestData },
    });
    logToConsole(entry);
  }

  /**
   * Logs component lifecycle events
   * @param {string} componentName - Name of the component
   * @param {string} event - Event type (mount, unmount, error, etc.)
   * @param {any} data - Additional data (optional)
   */
  static logComponentEvent(
    componentName: string,
    event: string,
    data?: any
  ): void {
    const entry = createLogEntry({
      level: "debug",
      category: "component",
      operation: event,
      message: `Component ${componentName} ${event}`,
      data: { componentName, ...data },
    });
    logToConsole(entry);
  }

  /**
   * Logs hook usage and errors
   * @param {string} hookName - Name of the hook
   * @param {string} operation - Operation being performed
   * @param {Error} error - Error that occurred (optional)
   * @param {any} data - Additional data (optional)
   */
  static logHookEvent(
    hookName: string,
    operation: string,
    error?: Error,
    data?: any
  ): void {
    const entry = createLogEntry({
      level: error ? "error" : "debug",
      category: "hook",
      operation: `${hookName}_${operation}`,
      message: error
        ? `Hook ${hookName} error during ${operation}`
        : `Hook ${hookName} ${operation}`,
      error: error
        ? {
            name: error.name,
            message: error.message,
            stack: error.stack,
          }
        : undefined,
      data: { hookName, operation, ...data },
    });
    logToConsole(entry);
  }

  /**
   * Logs general warning messages
   * @param {string} operation - Operation context
   * @param {string} message - Warning message
   * @param {any} data - Additional data (optional)
   */
  static logWarning(operation: string, message: string, data?: any): void {
    const entry = createLogEntry({
      level: "warn",
      category: "notification",
      operation,
      message,
      data,
    });
    logToConsole(entry);
  }
}
