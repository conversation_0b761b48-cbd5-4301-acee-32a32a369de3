import { NextResponse } from "next/server";

/**
 * Utility function to handle API responses and forward 401 errors properly
 * @param {Response} response - The fetch response from backend API
 * @param {any} data - The parsed JSON data from the response
 * @param {string} defaultErrorMessage - Default error message if none provided
 * @return {NextResponse} NextResponse with appropriate status and data
 */
export function handleApiResponse(
  response: Response,
  data: any,
  defaultErrorMessage: string = "Request failed"
): NextResponse {
  if (response.ok) {
    return NextResponse.json(data);
  } else {
    // Forward the exact status code from backend, including 401
    return NextResponse.json(
      {
        success: false,
        message: data.message || defaultErrorMessage,
      },
      { status: response.status }
    );
  }
}

/**
 * Utility function to make authenticated fetch requests with proper error handling
 * @param {string} url - The URL to fetch
 * @param {RequestInit} options - Fetch options
 * @param {string} accessToken - The access token for authentication
 * @return {Promise} Promise with response and parsed data
 */
export async function authenticatedFetch(
  url: string,
  options: RequestInit = {},
  accessToken: string
): Promise<{ response: Response; data: any }> {
  const response = await fetch(url, {
    ...options,
    headers: {
      Authorization: `Bearer ${accessToken}`,
      "Content-Type": "application/json",
      ...options.headers,
    },
    cache: "no-store",
  });

  const data = await response.json();
  return { response, data };
}
