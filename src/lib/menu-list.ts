import { IdCardIcon } from "@radix-ui/react-icons";
import {
  BookTextIcon,
  CreditCard,
  EuroIcon,
  FileText,
  Headset,
  LayoutGrid,
  Package,
  SquareArrowDown,
  StarIcon,
  User,
  UserCircle,
  Workflow,
  GitBranch,
  Bell,
  ClipboardList,
  Users,
} from "lucide-react";

type Submenu = {
  href: string;
  label: string;
  active: boolean;
};

type Menu = {
  href: string;
  label: string;
  active: boolean;
  icon: any;
  submenus: Submenu[];
};

type Group = {
  groupLabel: string;
  menus: Menu[];
};

export function getMenuList(
  pathname: string,
  userRole?: "user" | "admin" | "agent"
): Group[] {
  // If userRole is not yet determined, return empty menu to prevent showing admin menus
  if (!userRole) {
    return [];
  }

  // Agent users only see Applications and Profile menus
  if (userRole === "agent") {
    return [
      {
        groupLabel: "",
        menus: [
          {
            href: "/applications",
            label: "Applications",
            active: pathname === "/applications",
            icon: ClipboardList,
            submenus: [],
          },
        ],
      },
      {
        groupLabel: "Account",
        menus: [
          {
            href: "/profile",
            label: "Profile",
            active: pathname === "/profile",
            icon: User,
            submenus: [],
          },
        ],
      },
    ];
  }

  // Admin users see all menus EXCEPT Profile menu
  // Only show admin menus if userRole is explicitly "admin"
  if (userRole === "admin") {
    return [
      {
        groupLabel: "",
        menus: [
          {
            href: "/",
            label: "Dashboard",
            active: pathname === "/",
            icon: LayoutGrid,
            submenus: [],
          },
        ],
      },
      {
        groupLabel: "Contents",
        menus: [
          {
            href: "/user",
            label: "User",
            active: pathname === "/user",
            icon: User,
            submenus: [],
          },
          {
            href: "/mentor",
            label: "Mentor",
            active: pathname === "/mentor",
            icon: UserCircle,
            submenus: [],
          },
          {
            href: "/our-package",
            label: "Our Packages",
            active: pathname === "/our-package",
            icon: Package,
            submenus: [],
          },
          {
            href: "/immigration",
            label: "Immigration Services",
            active: pathname === "/immigration",
            icon: IdCardIcon,
            submenus: [],
          },
          {
            href: "/training",
            label: "Training",
            active: pathname === "/training",
            icon: SquareArrowDown,
            submenus: [],
          },
          {
            href: "/blog",
            label: "Blog",
            active: pathname === "/blog",
            icon: BookTextIcon,
            submenus: [],
          },
          {
            href: "/guest",
            label: "Guest Purchase",
            active: pathname === "/guest",
            icon: EuroIcon,
            submenus: [],
          },
          {
            href: "/purchases",
            label: "Purchases",
            active: pathname === "/purchases",
            icon: CreditCard,
            submenus: [],
          },
          {
            href: "/customer-review",
            label: "Customer review",
            active: pathname === "/customer-review",
            icon: StarIcon,
            submenus: [],
          },
          {
            href: "/contact-us",
            label: "Contact Us",
            active: pathname === "/contact-us",
            icon: Headset,
            submenus: [],
          },
        ],
      },
      {
        groupLabel: "Management",
        menus: [
          {
            href: "/applications",
            label: "Applications",
            active: pathname === "/applications",
            icon: ClipboardList,
            submenus: [],
          },
        ],
      },
      {
        groupLabel: "Settings",
        menus: [
          {
            href: "/agents",
            label: "Agents",
            active: pathname === "/agents",
            icon: Users,
            submenus: [],
          },
          {
            href: "/immigration-documents",
            label: "Document Master",
            active: pathname === "/immigration-documents",
            icon: FileText,
            submenus: [],
          },
          {
            href: "/workflow-master",
            label: "Workflow Master",
            active: pathname === "/workflow-master",
            icon: Workflow,
            submenus: [],
          },
          {
            href: "/workflow-templates",
            label: "Workflow Templates",
            active: pathname === "/workflow-templates",
            icon: GitBranch,
            submenus: [],
          },
          {
            href: "/notifications",
            label: "Notifications",
            active: pathname === "/notifications",
            icon: Bell,
            submenus: [],
          },
        ],
      },
      // NOTE: Profile menu is intentionally excluded for admin users
    ];
  }

  // For any other role (including "user"), return empty menu
  return [];
}
