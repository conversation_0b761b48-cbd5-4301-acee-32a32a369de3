import axios from "axios";
import { signOut } from "next-auth/react";
import { toast } from "sonner";
import { apiUrl } from "@/utils/urls";

// Create axios instance
const axiosInstance = axios.create({
  baseURL: apiUrl,
});

// Flag to prevent multiple logout calls
let isLoggingOut = false;

// Response interceptor to handle 401 errors
axiosInstance.interceptors.response.use(
  (response) => response,
  async (error) => {
    // const originalRequest = error.config;

    // Check if error is 401 and we haven't already started logout process
    if (error.response?.status === 401 && !isLoggingOut) {
      isLoggingOut = true;

      try {
        // Show user-friendly message
        toast.error("Your session has expired. Please log in again.", {
          duration: 3000,
        });

        // Sign out user and redirect to login
        await signOut({
          callbackUrl: "/signin",
          redirect: true,
        });
      } catch (signOutError) {
        console.error("Error during logout:", signOutError);
        // Fallback: redirect manually
        window.location.href = "/signin";
      } finally {
        isLoggingOut = false;
      }
    }

    return Promise.reject(error);
  }
);

export default axiosInstance;
