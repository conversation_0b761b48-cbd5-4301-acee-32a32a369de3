import { useEffect, useCallback } from "react";
import { useSession, signOut } from "next-auth/react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

export const useAuthErrorHandler = () => {
  const { data: session, status } = useSession();
  const router = useRouter();

  const handleAuthError = useCallback(async () => {
    try {
      toast.error("Your session has expired. Please log in again.", {
        duration: 3000,
      });

      // Sign out and redirect to login
      await signOut({
        callbackUrl: "/signin",
        redirect: true,
      });
    } catch (error) {
      console.error("Error during logout:", error);
      // Fallback: redirect manually
      router.push("/signin");
    }
  }, [router]);

  useEffect(() => {
    // Check if session has error (from token refresh failure)
    if (session?.error === "RefreshAccessTokenError") {
      handleAuthError();
    }
  }, [session, handleAuthError]);

  const handle401Error = async () => {
    await handleAuthError();
  };

  return {
    handle401Error,
    isAuthenticated: status === "authenticated" && !session?.error,
    isLoading: status === "loading",
  };
};
