export { default } from "next-auth/middleware";

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - signin (login page)
     * - signup (registration page)
     * - admin/forgot-password (admin password reset request page)
     * - admin/reset-password (admin password reset form page)
     * - agent/forgot-password (agent password reset request page)
     * - agent/reset-password (agent password reset form page)
     */
    "/((?!api|_next/static|_next/image|favicon.ico|signin|signup|admin/forgot-password|admin/reset-password|agent/forgot-password|agent/reset-password).*)",
  ],
};
