/**
 * Form validation utilities for Applications module
 * Provides comprehensive validation logic for required fields, change detection, and error handling
 */

export interface ValidationError {
  field: string;
  message: string;
  type: "required" | "invalid" | "custom";
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  hasChanges: boolean;
}

export interface FormFieldConfig {
  fieldName: string;
  fieldType: string;
  required: boolean;
  label?: string;
}

/**
 * Validates required fields in form data
 * @param {Record<string, any>} formData - The form data to validate
 * @param {FormFieldConfig[]} fieldConfigs - Configuration for form fields
 * @return {ValidationError[]} Array of validation errors
 */
export function validateRequiredFields(
  formData: Record<string, any>,
  fieldConfigs: FormFieldConfig[]
): ValidationError[] {
  const errors: ValidationError[] = [];

  fieldConfigs.forEach((config) => {
    if (config.required) {
      const value = formData[config.fieldName];
      const isEmpty = isFieldEmpty(value, config.fieldType);

      if (isEmpty) {
        errors.push({
          field: config.fieldName,
          message: `${config.label || config.fieldName} is required`,
          type: "required",
        });
      }
    }
  });

  return errors;
}

/**
 * Checks if a field value is considered empty based on its type
 * @param {any} value - The field value to check
 * @param {string} fieldType - The type of the field
 * @return {boolean} True if the field is empty
 */
export function isFieldEmpty(value: any, fieldType: string): boolean {
  if (value === null || value === undefined) {
    return true;
  }

  switch (fieldType) {
    case "text":
    case "email":
    case "tel":
    case "textarea":
    case "select":
    case "radio":
      return typeof value === "string" && value.trim() === "";

    case "number":
      return value === "" || isNaN(Number(value));

    case "date":
      return value === "" || !isValidDate(value);

    case "checkbox":
      return !Array.isArray(value) || value.length === 0;

    default:
      return value === "" || value === null || value === undefined;
  }
}

/**
 * Validates if a date string is valid
 * @param {string} dateString - The date string to validate
 * @return {boolean} True if the date is valid
 */
function isValidDate(dateString: string): boolean {
  if (!dateString) return false;
  const date = new Date(dateString);
  return date instanceof Date && !isNaN(date.getTime());
}

/**
 * Detects if form data has changed compared to initial data
 * @param {Record<string, any>} currentData - The current form data
 * @param {Record<string, any>} initialData - The initial form data
 * @return {boolean} True if the form data has changed
 */
export function detectFormChanges(
  currentData: Record<string, any>,
  initialData: Record<string, any>
): boolean {
  // Deep comparison of form data
  return (
    JSON.stringify(normalizeFormData(currentData)) !==
    JSON.stringify(normalizeFormData(initialData))
  );
}

/**
 * Normalizes form data for comparison (handles undefined/null values)
 * @param {Record<string, any>} data - The form data to normalize
 * @return {Record<string, any>} The normalized form data
 */
function normalizeFormData(data: Record<string, any>): Record<string, any> {
  const normalized: Record<string, any> = {};

  Object.keys(data).forEach((key) => {
    const value = data[key];
    if (value === null || value === undefined || value === "") {
      normalized[key] = "";
    } else if (Array.isArray(value)) {
      normalized[key] = value.sort();
    } else {
      normalized[key] = value;
    }
  });

  return normalized;
}

/**
 * Validates document upload requirements
 * @param {Array} requiredDocuments - Required documents configuration
 * @param {Array} uploadedDocuments - Uploaded documents data
 * @return {ValidationError[]} Array of validation errors
 */
export function validateDocumentRequirements(
  requiredDocuments: Array<{ documentName: string; required: boolean }>,
  uploadedDocuments: Array<{ document_name: string; status?: string }>
): ValidationError[] {
  const errors: ValidationError[] = [];

  requiredDocuments.forEach((reqDoc) => {
    if (reqDoc.required) {
      const uploaded = uploadedDocuments.find(
        (doc) => doc.document_name === reqDoc.documentName
      );

      if (!uploaded) {
        errors.push({
          field: reqDoc.documentName,
          message: `${reqDoc.documentName} is required`,
          type: "required",
        });
      } else if (uploaded.status === "Rejected") {
        errors.push({
          field: reqDoc.documentName,
          message: `${reqDoc.documentName} was rejected and needs to be re-uploaded`,
          type: "invalid",
        });
      }
    }
  });

  return errors;
}

/**
 * Formats validation errors for display
 * @param {ValidationError[]} errors - Array of validation errors
 * @return {string} Formatted error message
 */
export function formatValidationErrors(errors: ValidationError[]): string {
  if (errors.length === 0) return "";

  if (errors.length === 1) {
    return errors[0].message;
  }

  const requiredErrors = errors.filter((e) => e.type === "required");
  const otherErrors = errors.filter((e) => e.type !== "required");

  let message = "";

  if (requiredErrors.length > 0) {
    message += `Please fill in the following required fields: ${requiredErrors.map((e) => e.field).join(", ")}`;
  }

  if (otherErrors.length > 0) {
    if (message) message += ". ";
    message += otherErrors.map((e) => e.message).join(". ");
  }

  return message;
}

/**
 * Gets field configuration from workflow form fields
 * @param {Array} fields - Workflow form fields
 * @return {FormFieldConfig[]} Array of field configurations
 */
export function getFieldConfigs(
  fields: Array<{ fieldName: string; fieldType: string; required: boolean }>
): FormFieldConfig[] {
  return fields.map((field) => ({
    fieldName: field.fieldName,
    fieldType: field.fieldType,
    required: field.required,
    label: field.fieldName,
  }));
}

/**
 * Comprehensive form validation that combines all validation types
 * @param {Object} currentData - Current form data
 * @param {Object} initialData - Initial form data
 * @param {Array} fieldConfigs - Field configuration array
 * @param {Array} requiredDocuments - Required documents configuration
 * @param {Array} uploadedDocuments - Uploaded documents data
 * @return {ValidationResult} Validation result object
 */
export function validateForm(
  currentData: Record<string, any>,
  initialData: Record<string, any>,
  fieldConfigs: FormFieldConfig[],
  requiredDocuments?: Array<{ documentName: string; required: boolean }>,
  uploadedDocuments?: Array<{ document_name: string; status?: string }>
): ValidationResult {
  const fieldErrors = validateRequiredFields(currentData, fieldConfigs);
  const documentErrors =
    requiredDocuments && uploadedDocuments
      ? validateDocumentRequirements(requiredDocuments, uploadedDocuments)
      : [];

  const allErrors = [...fieldErrors, ...documentErrors];
  const hasChanges = detectFormChanges(currentData, initialData);

  return {
    isValid: allErrors.length === 0,
    errors: allErrors,
    hasChanges,
  };
}
