"use client";
import { blogSchema } from "@/utils/schema";
import React from "react";
import { UseFormReturn } from "react-hook-form";
import { z } from "zod";
import { useEditor, EditorContent } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Image from "@tiptap/extension-image";
import Youtube from "@tiptap/extension-youtube";
import TextAlign from "@tiptap/extension-text-align";
import Link from "@tiptap/extension-link";
import { ContextMenu, ContextMenuTrigger } from "@/components/ui/context-menu";
import Underline from "@tiptap/extension-underline";
import Color from "@tiptap/extension-color";
import TextStyle from "@tiptap/extension-text-style";
import Blockquote from "@tiptap/extension-blockquote";
import Heading from "@tiptap/extension-heading";
import Bold from "@tiptap/extension-bold";
import MenuBar from "./menu-bar";

interface IEditorProp {
  form: UseFormReturn<z.infer<typeof blogSchema>>;
}

const Editor: React.FC<IEditorProp> = ({ form }) => {
  const editor = useEditor({
    extensions: [
      StarterKit,
      Image.configure({
        HTMLAttributes: {
          class: "aspect-auto w-full",
        },
      }),
      Underline,
      TextStyle,
      Color,
      Blockquote.configure({
        HTMLAttributes: {
          class: "dark:text-white text-black",
        },
      }),
      Bold.configure({
        HTMLAttributes: {
          class: "dark:text-white text-black",
        },
      }),
      Heading.configure({
        HTMLAttributes: {
          class: "dark:text-white text-black",
        },
      }),
      TextAlign.configure({
        types: ["heading", "paragraph"],
        defaultAlignment: "left",
        alignments: ["left", "center", "right", "justify"],
      }),

      Youtube.configure({
        controls: false,
      }),
      Link.configure({
        openOnClick: true,
        HTMLAttributes: {
          class:
            "font-bold hover:underline cursor-pointer no-underline text-blue-400",
        },
      }),
    ],
    editorProps: {
      attributes: {
        class: `prose   mt-6  p-2 focus:outline-none dark:text-white max-w-5xl `,
      },
    },
    content: form.watch("desc"),
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      form.setValue("desc", html);
    },
  });
  if (!editor) {
    return null;
  }
  return (
    <ContextMenu>
      <ContextMenuTrigger className="flex flex-col items-center  border w-full max-w-full  rounded-md ">
        <EditorContent editor={editor} />
      </ContextMenuTrigger>
      <MenuBar editor={editor} />
    </ContextMenu>
  );
};

export default Editor;
