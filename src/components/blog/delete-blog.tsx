import { useDeleteBlog } from "@/hooks/use-query";
import { TrashIcon } from "lucide-react";
import React from "react";
import { Button } from "../ui/button";

const DeleteBlog = ({ id }: { id: string }) => {
  const { mutate } = useDeleteBlog();
  return (
    <Button
      className="bg-[#ffebe6] text-[#fd381d]"
      variant="none"
      onClick={() => mutate(id)}
      size="icon"
    >
      <TrashIcon size={16} strokeWidth={2} aria-hidden="true" />
    </Button>
  );
};

export default DeleteBlog;
