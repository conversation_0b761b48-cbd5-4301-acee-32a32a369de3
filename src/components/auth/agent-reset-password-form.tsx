"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { toast } from "sonner";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  ArrowLeft,
  CheckCircle,
  AlertCircle,
  Eye,
  EyeOff,
  Users,
} from "lucide-react";
import { agentResetPasswordSchema } from "@/utils/schema";
import {
  agentResetPassword,
  getTokenFromUrl,
} from "@/lib/password-reset-service";

type AgentResetPasswordFormData = z.infer<typeof agentResetPasswordSchema>;

/**
 * Agent Reset Password Form Component
 * Handles password reset for agent users with token validation from URL
 * @return {JSX.Element} The agent reset password form component
 */
const AgentResetPasswordForm = () => {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [tokenError, setTokenError] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Form configuration
  const form = useForm<AgentResetPasswordFormData>({
    resolver: zodResolver(agentResetPasswordSchema),
    defaultValues: {
      token: "",
      newPassword: "",
      confirmPassword: "",
    },
  });

  /**
   * Validates token from URL on component mount
   */
  useEffect(() => {
    const token = getTokenFromUrl(searchParams);

    if (!token) {
      setTokenError(
        "No reset token found in URL. Please use the link from your email."
      );
      return;
    }

    // Set token in form
    form.setValue("token", token);
  }, [searchParams, form]);

  /**
   * Handles form submission for password reset
   * @param {AgentResetPasswordFormData} data - The form data
   */
  const onSubmit = async (data: AgentResetPasswordFormData) => {
    setIsLoading(true);

    try {
      const response = await agentResetPassword(
        data.token,
        data.newPassword,
        data.confirmPassword
      );

      if (response.success) {
        setIsSuccess(true);
        toast.success("Password reset successfully");
      } else {
        toast.error(response.message || "Failed to reset password");
      }
    } catch (error) {
      console.error("Agent reset password error:", error);
      toast.error("An error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Handles navigation back to sign in page
   */
  const handleBackToSignIn = () => {
    router.push("/signin");
  };

  /**
   * Handles requesting a new reset token
   */
  const handleRequestNewToken = () => {
    router.push("/forgot-password");
  };

  // Show error state for missing token
  if (tokenError) {
    return (
      <div className="w-full space-y-6">
        <div className="text-center space-y-4">
          <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
            <AlertCircle className="h-8 w-8 text-red-600" />
          </div>
          <div>
            <h2 className="text-xl font-semibold flex items-center justify-center gap-2">
              <Users className="h-5 w-5" />
              Invalid Agent Reset Link
            </h2>
            <p className="text-muted-foreground mt-2">{tokenError}</p>
          </div>
        </div>

        <div className="space-y-3">
          <Button onClick={handleRequestNewToken} className="w-full">
            Request new agent reset link
          </Button>
          <Button
            onClick={handleBackToSignIn}
            variant="ghost"
            className="w-full"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to sign in
          </Button>
        </div>
      </div>
    );
  }

  // Show success state after password reset
  if (isSuccess) {
    return (
      <div className="w-full space-y-6">
        <div className="text-center space-y-4">
          <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
          <div>
            <h2 className="text-xl font-semibold flex items-center justify-center gap-2">
              <Users className="h-5 w-5" />
              Agent Password Reset Successful
            </h2>
            <p className="text-muted-foreground mt-2">
              Your agent password has been successfully reset. You can now sign
              in with your new password.
            </p>
          </div>
        </div>

        <Button onClick={handleBackToSignIn} className="w-full">
          Continue to agent sign in
        </Button>
      </div>
    );
  }

  return (
    <div className="w-full space-y-6">
      <div className="text-center">
        <h2 className="text-xl font-semibold flex items-center justify-center gap-2">
          <Users className="h-5 w-5" />
          Reset Agent Password
        </h2>
        <p className="text-muted-foreground mt-2">
          Enter your new agent password below
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="newPassword"
            render={({ field }) => (
              <FormItem>
                <FormLabel>New Password</FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input
                      type={showPassword ? "text" : "password"}
                      placeholder="Enter your new password"
                      {...field}
                      disabled={isLoading}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowPassword(!showPassword)}
                      disabled={isLoading}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="confirmPassword"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Confirm New Password</FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input
                      type={showConfirmPassword ? "text" : "password"}
                      placeholder="Confirm your new password"
                      {...field}
                      disabled={isLoading}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() =>
                        setShowConfirmPassword(!showConfirmPassword)
                      }
                      disabled={isLoading}
                    >
                      {showConfirmPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="text-sm text-muted-foreground">
            <p>Password requirements:</p>
            <ul className="list-disc list-inside mt-1 space-y-1">
              <li>At least 8 characters long</li>
              <li>Contains uppercase and lowercase letters</li>
              <li>Contains at least one number</li>
            </ul>
          </div>

          <Button type="submit" className="w-full" disabled={isLoading}>
            {isLoading ? "Resetting Password..." : "Reset Password"}
          </Button>
        </form>
      </Form>

      <div className="text-center">
        <Button
          onClick={handleBackToSignIn}
          variant="ghost"
          className="text-sm"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to sign in
        </Button>
      </div>
    </div>
  );
};

export default AgentResetPasswordForm;
