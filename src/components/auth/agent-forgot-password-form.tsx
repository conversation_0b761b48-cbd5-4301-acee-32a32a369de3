"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { toast } from "sonner";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ArrowLeft, Mail, Users } from "lucide-react";
import { agentForgotPasswordSchema } from "@/utils/schema";
import { agentForgotPassword } from "@/lib/password-reset-service";

type AgentForgotPasswordFormData = z.infer<typeof agentForgotPasswordSchema>;

/**
 * Agent Forgot Password Form Component
 * Handles forgot password requests for agent users
 * @return {JSX.Element} The agent forgot password form component
 */
const AgentForgotPasswordForm = () => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [submittedEmail, setSubmittedEmail] = useState("");

  // Form configuration
  const form = useForm<AgentForgotPasswordFormData>({
    resolver: zodResolver(agentForgotPasswordSchema),
    defaultValues: {
      email: "",
    },
  });

  /**
   * Handles form submission for forgot password request
   * @param {AgentForgotPasswordFormData} data - The form data
   */
  const onSubmit = async (data: AgentForgotPasswordFormData) => {
    setIsLoading(true);

    try {
      const response = await agentForgotPassword(data.email);

      if (response.success) {
        setSubmittedEmail(data.email);
        setIsSubmitted(true);
        toast.success("Password reset instructions sent to your email");
      } else {
        toast.error(response.message || "Failed to send password reset email");
      }
    } catch (error) {
      console.error("Agent forgot password error:", error);
      toast.error("An error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Handles navigation back to sign in page
   */
  const handleBackToSignIn = () => {
    router.push("/signin");
  };

  /**
   * Handles resending password reset email
   */
  const handleResendEmail = () => {
    setIsSubmitted(false);
    form.reset();
  };

  // Show success message after successful submission
  if (isSubmitted) {
    return (
      <div className="w-full space-y-6">
        <div className="text-center space-y-4">
          <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
            <Mail className="h-8 w-8 text-green-600" />
          </div>
          <div>
            <h2 className="text-xl font-semibold flex items-center justify-center gap-2">
              <Users className="h-5 w-5" />
              Check your agent email
            </h2>
            <p className="text-muted-foreground mt-2">
              We&apos;ve sent agent password reset instructions to{" "}
              <span className="font-medium">{submittedEmail}</span>
            </p>
          </div>
        </div>

        <div className="space-y-3">
          <Button
            onClick={handleResendEmail}
            variant="outline"
            className="w-full"
          >
            Send another agent reset email
          </Button>
          <Button
            onClick={handleBackToSignIn}
            variant="ghost"
            className="w-full"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to sign in
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full space-y-6">
      <div className="text-center">
        <h2 className="text-xl font-semibold flex items-center justify-center gap-2">
          <Users className="h-5 w-5" />
          Agent Forgot Password
        </h2>
        <p className="text-muted-foreground mt-2">
          Enter your agent email address and we&apos;ll send you a link to reset
          your password
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Agent Email</FormLabel>
                <FormControl>
                  <Input
                    type="email"
                    placeholder="<EMAIL>"
                    {...field}
                    disabled={isLoading}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <Button type="submit" className="w-full" disabled={isLoading}>
            {isLoading ? "Sending..." : "Send agent reset instructions"}
          </Button>
        </form>
      </Form>

      <div className="text-center">
        <Button
          onClick={handleBackToSignIn}
          variant="ghost"
          className="text-sm"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to sign in
        </Button>
      </div>
    </div>
  );
};

export default AgentForgotPasswordForm;
