"use client";

import React, { useState, use<PERSON><PERSON>back, useMemo } from "react";
import { More<PERSON><PERSON><PERSON><PERSON>, Edit, Trash2, Search } from "lucide-react";
import { format } from "date-fns";

import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { FilterSelect } from "@/components/ui/filter-select";
import { DataTablePagination } from "@/components/ui/data-table-pagination";
import { EditWorkflowTemplateDialog } from "./edit-workflow-template-dialog";
import { DeleteWorkflowTemplateDialog } from "./delete-workflow-template-dialog";
import { useUpdateWorkflowTemplateDefault } from "@/hooks/use-query";

interface WorkflowTemplatesDataTableProps {
  data: IWorkflowTemplate[];
  loading: boolean;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  onPageChange: (page: number) => void;
  onSearch: (query: string) => void;
  onServiceTypeFilter: (serviceType: string) => void;
  onActiveFilter: (isActive: boolean | undefined) => void;
  onUpdateSuccess: () => void;
  onDeleteSuccess: () => void;
  immigrationPackages?: IImmigration[]; // Immigration packages for package name lookup
}

export const WorkflowTemplatesDataTable: React.FC<
  WorkflowTemplatesDataTableProps
> = ({
  data,
  loading,
  pagination,
  onPageChange,
  onSearch,
  onServiceTypeFilter,
  onActiveFilter,
  onUpdateSuccess,
  onDeleteSuccess,
  immigrationPackages = [],
}) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [activeFilter, setActiveFilter] = useState("all");
  const [editingTemplate, setEditingTemplate] =
    useState<IWorkflowTemplate | null>(null);
  const [deletingTemplate, setDeletingTemplate] =
    useState<IWorkflowTemplate | null>(null);

  // Hook for updating default status
  const updateDefaultMutation = useUpdateWorkflowTemplateDefault();

  // Helper function to get package name
  const getPackageName = useCallback(
    (serviceId: string) => {
      const immigrationPackage = immigrationPackages.find(
        (pkg) => pkg.id === serviceId
      );
      return immigrationPackage?.name || "N/A";
    },
    [immigrationPackages]
  );

  // Handle default status toggle
  const handleDefaultToggle = useCallback(
    async (templateId: string, currentDefault: boolean) => {
      try {
        await updateDefaultMutation.mutateAsync({
          id: templateId,
          isDefault: !currentDefault,
        });
        onUpdateSuccess(); // Refresh the data
      } catch (error) {
        console.error("Error updating default status:", error);
      }
    },
    [updateDefaultMutation, onUpdateSuccess]
  );

  // Debounced search
  const handleSearchChange = useCallback(
    (value: string) => {
      setSearchQuery(value);
      const timeoutId = setTimeout(() => {
        onSearch(value);
      }, 300);
      return () => clearTimeout(timeoutId);
    },
    [onSearch]
  );

  const activeOptions = useMemo(
    () => [
      { value: "all", label: "All Status" },
      { value: "true", label: "Active" },
      { value: "false", label: "Inactive" },
    ],
    []
  );

  const handleEdit = useCallback((template: IWorkflowTemplate) => {
    setEditingTemplate(template);
  }, []);

  const handleDelete = useCallback((template: IWorkflowTemplate) => {
    setDeletingTemplate(template);
  }, []);

  const handleEditSuccess = useCallback(() => {
    // Use setTimeout to ensure modal cleanup happens after state updates
    setTimeout(() => {
      setEditingTemplate(null);
    }, 100);
    // Call success handler to refresh data
    onUpdateSuccess();
  }, [onUpdateSuccess]);

  const handleDeleteSuccess = useCallback(() => {
    // Use setTimeout to ensure modal cleanup happens after state updates
    setTimeout(() => {
      setDeletingTemplate(null);
    }, 100);
    // Call success handler to refresh data
    onDeleteSuccess();
  }, [onDeleteSuccess]);

  const formatDate = (dateString?: string) => {
    if (!dateString) return "N/A";
    try {
      return format(new Date(dateString), "MMM dd, yyyy");
    } catch {
      return "Invalid Date";
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading workflow templates...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search templates..."
            value={searchQuery}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="pl-10"
          />
        </div>
        <div className="flex gap-2">
          <FilterSelect
            value={activeFilter}
            options={activeOptions}
            placeholder="Status"
            onValueChange={(value) => {
              setActiveFilter(value);
              onActiveFilter(value === "all" ? undefined : value === "true");
            }}
          />
        </div>
      </div>

      {/* Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Package Name</TableHead>
              <TableHead>Stages</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Default</TableHead>
              <TableHead>Created</TableHead>
              <TableHead>Updated</TableHead>
              <TableHead className="w-[70px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-8">
                  No workflow templates found
                </TableCell>
              </TableRow>
            ) : (
              data.map((template) => (
                <TableRow key={template.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{template.name}</div>
                      {template.description && (
                        <div className="text-sm text-muted-foreground truncate max-w-[200px]">
                          {template.description}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="font-medium">
                      {template.packageName ||
                        getPackageName(template.serviceId || "")}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="secondary">
                      {template.workflowTemplate?.length || 0} stages
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant={template.isActive ? "default" : "secondary"}
                    >
                      {template.isActive ? "Active" : "Inactive"}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={template.isDefault || false}
                        onCheckedChange={() =>
                          handleDefaultToggle(
                            template.id!,
                            template.isDefault || false
                          )
                        }
                        disabled={updateDefaultMutation.isPending}
                        aria-label={`Set as default template`}
                      />
                      <span className="text-sm text-muted-foreground">
                        {template.isDefault ? "Default" : ""}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="text-sm text-muted-foreground">
                    {formatDate(template.createdAt)}
                  </TableCell>
                  <TableCell className="text-sm text-muted-foreground">
                    {formatDate(template.updatedAt)}
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleEdit(template)}>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleDelete(template)}
                          className="text-destructive"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <DataTablePagination
        currentPage={pagination.page}
        totalPages={pagination.totalPages}
        totalItems={pagination.total}
        itemsPerPage={pagination.limit}
        onPageChange={onPageChange}
      />

      {/* Edit Dialog */}
      <EditWorkflowTemplateDialog
        template={editingTemplate}
        open={!!editingTemplate}
        onOpenChange={(open) => {
          if (!open) {
            // Use setTimeout to ensure proper modal cleanup and prevent interaction blocking
            setTimeout(() => {
              setEditingTemplate(null);
            }, 50);
          }
        }}
        onSuccess={handleEditSuccess}
      />

      {/* Delete Dialog */}
      <DeleteWorkflowTemplateDialog
        template={deletingTemplate}
        open={!!deletingTemplate}
        onOpenChange={(open) => {
          if (!open) {
            // Use setTimeout to ensure proper modal cleanup and prevent interaction blocking
            setTimeout(() => {
              setDeletingTemplate(null);
            }, 50);
          }
        }}
        onSuccess={handleDeleteSuccess}
      />
    </div>
  );
};
