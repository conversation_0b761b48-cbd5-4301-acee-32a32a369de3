"use client";
import React, { useState } from "react";
import { Dialog, DialogTrigger } from "@/components/ui/dialog";
import { PlusIcon } from "lucide-react";
import ImmigrationForm from "./immigration-form";

/**
 * Create Immigration Dialog Component
 * Handles the modal state for creating new immigration services
 * Provides a clean separation between create and edit functionality
 * @return {JSX.Element} The create immigration dialog component
 */
const CreateImmigrationDialog: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);

  /**
   * Handles successful form submission by closing the modal
   */
  const handleSuccess = () => {
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger className="bg-black p-2 text-white rounded-lg dark:bg-white dark:text-black">
        <PlusIcon />
      </DialogTrigger>
      <ImmigrationForm immigration={undefined} onSuccess={handleSuccess} />
    </Dialog>
  );
};

export default CreateImmigrationDialog;
