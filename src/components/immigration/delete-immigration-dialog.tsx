"use client";

import React from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useRemoveImmigration } from "@/hooks/use-query";

interface DeleteImmigrationDialogProps {
  immigration: IImmigration | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

/**
 * Delete confirmation dialog for immigration services
 * Handles deletion with proper error handling for conflict responses
 * @param {Object} props - Component props
 * @param {IImmigration | null} props.immigration - Immigration service to delete
 * @param {boolean} props.open - Dialog open state
 * @param {Function} props.onOpenChange - Dialog state change handler
 * @param {Function} props.onSuccess - Success callback after deletion
 * @return {JSX.Element | null} The delete confirmation dialog
 */
export const DeleteImmigrationDialog: React.FC<
  DeleteImmigrationDialogProps
> = ({ immigration, open, onOpenChange, onSuccess }) => {
  const deleteMutation = useRemoveImmigration();

  /**
   * Handles the delete operation with proper error handling
   * Closes dialog and calls success callback on successful deletion
   */
  const handleDelete = async () => {
    if (!immigration?.id) return;

    try {
      await deleteMutation.mutateAsync(immigration.id);
      onOpenChange(false);
      onSuccess();
    } catch (error) {
      // Error is handled by the mutation with user-friendly messages
      console.error("Error deleting immigration service:", error);
    }
  };

  if (!immigration) return null;

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Delete Immigration Service</AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to delete the immigration service &quot;
            {immigration.name}&quot;? This action cannot be undone and will
            permanently remove the service from the system.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={deleteMutation.isPending}>
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={deleteMutation.isPending}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {deleteMutation.isPending ? "Deleting..." : "Delete"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
