"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>T<PERSON><PERSON>,
  DialogClose,
} from "@/components/ui/dialog";
import { Mail, Send, Loader2, AlertCircle, RefreshCw, Eye } from "lucide-react";
import {
  usePendingNotifications,
  useSendNotification,
} from "@/hooks/use-query";
import { useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { NotificationLogger } from "@/lib/notification-logger";

interface DocumentStatusNotificationProps {
  applicationId: string;
  applicationNumber?: string;
  className?: string;
}

/**
 * Validates and normalizes notification data structure
 * @param {any} data - Raw notification data from API
 * @return {NotificationPendingResponse | null} Validated notification data or null
 */
const validateNotificationData = (
  data: any
): NotificationPendingResponse | null => {
  if (!data) return null;

  // Check if data has required properties
  if (!data.notification_queue_id || !data.emailPreview) {
    return null;
  }

  // Validate emailPreview structure
  if (!data.emailPreview.subject || !data.emailPreview.htmlContent) {
    return null;
  }

  return {
    notification_queue_id: data.notification_queue_id,
    applicationNumber: data.applicationNumber || "",
    emailPreview: {
      subject: data.emailPreview.subject,
      htmlContent: data.emailPreview.htmlContent,
    },
  };
};

/**
 * DocumentStatusNotification Component
 *
 * Displays pending document status notifications and provides functionality
 * to send notification emails. Automatically checks for pending notifications
 * and updates after successful email sends.
 *
 * @param {string} applicationId - The application ID to check for notifications
 * @param {string} applicationNumber - Optional application number for display
 * @param {string} className - Optional CSS classes for styling
 * @return {JSX.Element|null} The notification component or null if no notifications
 */
export const DocumentStatusNotification: React.FC<
  DocumentStatusNotificationProps
> = ({ applicationId, applicationNumber, className = "" }) => {
  const queryClient = useQueryClient();
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Fetch pending notifications
  const {
    data: rawNotificationData,
    isLoading: isLoadingNotifications,
    error: notificationError,
    refetch: refetchNotifications,
  } = usePendingNotifications(applicationId);

  // Validate and normalize notification data
  const pendingNotification = validateNotificationData(rawNotificationData);

  // Send notification mutation
  const sendNotificationMutation = useSendNotification();

  /**
   * Cleanup effect to prevent memory leaks
   * Closes modal and resets states when component unmounts or applicationId changes
   */
  useEffect(() => {
    return () => {
      // Cleanup modal state on unmount
      setIsPreviewModalOpen(false);
      setIsRefreshing(false);
    };
  }, [applicationId]);

  /**
   * Handles sending the notification email
   * Sends the email and refetches pending notifications afterward
   */
  const handleSendNotification = async () => {
    if (!pendingNotification || !pendingNotification.notification_queue_id) {
      NotificationLogger.logWarning(
        "send_notification_missing_queue_id",
        "Attempted to send notification without valid notification data",
        { applicationId, hasData: !!rawNotificationData }
      );

      toast.error("No notification queue ID available", {
        description: "Please refresh the page and try again",
      });
      return;
    }

    try {
      NotificationLogger.logComponentEvent(
        "DocumentStatusNotification",
        "send_notification_start",
        {
          applicationId,
          queueId: pendingNotification.notification_queue_id,
        }
      );

      await sendNotificationMutation.mutateAsync({
        notification_queue_id: pendingNotification.notification_queue_id,
      });

      // Refetch pending notifications after successful send
      await refetchNotifications();

      // Invalidate related queries to ensure fresh data
      queryClient.invalidateQueries({
        queryKey: ["notifications", "pending", applicationId],
      });

      NotificationLogger.logComponentEvent(
        "DocumentStatusNotification",
        "send_notification_complete",
        { applicationId }
      );
    } catch (error) {
      // Enhanced error logging
      NotificationLogger.logComponentEvent(
        "DocumentStatusNotification",
        "send_notification_error",
        {
          applicationId,
          error: error instanceof Error ? error.message : String(error),
        }
      );
    }
  };

  /**
   * Handles manual refresh of pending notifications
   */
  const handleRefresh = async () => {
    if (isRefreshing) return; // Prevent multiple simultaneous refreshes

    setIsRefreshing(true);
    try {
      NotificationLogger.logComponentEvent(
        "DocumentStatusNotification",
        "refresh_notifications_start",
        { applicationId }
      );

      await refetchNotifications();

      toast.success("Notifications refreshed", {
        description: "Notification data has been updated",
      });

      NotificationLogger.logComponentEvent(
        "DocumentStatusNotification",
        "refresh_notifications_success",
        { applicationId }
      );
    } catch (error) {
      NotificationLogger.logComponentEvent(
        "DocumentStatusNotification",
        "refresh_notifications_error",
        {
          applicationId,
          error: error instanceof Error ? error.message : String(error),
        }
      );

      toast.error("Failed to refresh notifications", {
        description: "Please try again later",
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  /**
   * Handles opening the email template preview modal
   */
  const handlePreviewTemplate = () => {
    NotificationLogger.logComponentEvent(
      "DocumentStatusNotification",
      "preview_template_opened",
      { applicationId, hasValidData: !!pendingNotification }
    );
    setIsPreviewModalOpen(true);
  };

  /**
   * Handles closing the email template preview modal
   */
  const handleClosePreview = () => {
    setIsPreviewModalOpen(false);
    NotificationLogger.logComponentEvent(
      "DocumentStatusNotification",
      "preview_template_closed",
      { applicationId }
    );
  };

  // Enhanced validation - check for malformed data
  const hasValidData =
    pendingNotification &&
    pendingNotification.notification_queue_id &&
    pendingNotification.emailPreview &&
    pendingNotification.emailPreview.subject &&
    pendingNotification.emailPreview.htmlContent;

  // Don't render if there's no pending notification and not loading
  if (!isLoadingNotifications && !hasValidData && !notificationError) {
    // Log when component doesn't render due to no data
    if (rawNotificationData) {
      NotificationLogger.logWarning(
        "invalid_notification_data",
        "Component not rendering due to invalid notification data structure",
        { applicationId, rawData: rawNotificationData }
      );
    }
    return null;
  }

  // Error state
  if (notificationError && !isLoadingNotifications) {
    return (
      <Card className={`border-orange-200 bg-orange-50 ${className}`}>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-orange-800">
            <AlertCircle className="h-4 w-4" />
            Notification Service Issue
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-orange-700 mb-3">
            Unable to check for pending notifications. This may be temporary.
          </p>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            className="border-orange-300 text-orange-700 hover:bg-orange-100"
          >
            <RefreshCw className="h-3 w-3 mr-2" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  // Loading state
  if (isLoadingNotifications) {
    return (
      <Card className={`border-blue-200 bg-blue-50 ${className}`}>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-blue-800">
            <Loader2 className="h-4 w-4 animate-spin" />
            Checking for Notifications
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-blue-700">
            Checking for pending document status notifications...
          </p>
        </CardContent>
      </Card>
    );
  }

  // Handle case where we have raw data but validation failed
  if (!isLoadingNotifications && rawNotificationData && !pendingNotification) {
    return (
      <Card className={`border-orange-200 bg-orange-50 ${className}`}>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-orange-800">
            <AlertCircle className="h-4 w-4" />
            Invalid Notification Data
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-orange-700 mb-3">
            Received notification data but it appears to be malformed or
            incomplete.
          </p>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            className="border-orange-300 text-orange-700 hover:bg-orange-100"
          >
            <RefreshCw className="h-3 w-3 mr-2" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  // Render the component with modal always available
  return (
    <>
      {/* Main notification display */}
      {hasValidData && pendingNotification && (
        <Card className={`border-green-200 bg-green-50 ${className}`}>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-green-800">
              <Mail className="h-4 w-4" />
              Document Status Email Ready
              <Badge variant="secondary" className="ml-2">
                {applicationNumber || pendingNotification.applicationNumber}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Email Preview */}
            <div className="space-y-2">
              <div>
                <span className="text-sm font-medium text-green-800">
                  Subject:
                </span>
                <p className="text-sm text-green-700 mt-1">
                  {pendingNotification.emailPreview.subject}
                </p>
              </div>

              <div>
                <span className="text-sm font-medium text-green-800">
                  Preview:
                </span>
                <div
                  className="text-sm text-green-700 mt-1 p-2 bg-white rounded border max-h-32 overflow-y-auto"
                  dangerouslySetInnerHTML={{
                    __html:
                      pendingNotification.emailPreview.htmlContent.substring(
                        0,
                        200
                      ) + "...",
                  }}
                />
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center gap-2">
              <Button
                onClick={handleSendNotification}
                disabled={sendNotificationMutation.isPending || isRefreshing}
                className="bg-green-600 hover:bg-green-700 text-white"
                size="sm"
              >
                {sendNotificationMutation.isPending ? (
                  <>
                    <Loader2 className="h-3 w-3 mr-2 animate-spin" />
                    Sending...
                  </>
                ) : (
                  <>
                    <Send className="h-3 w-3 mr-2" />
                    Send Document Status Email
                  </>
                )}
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={handlePreviewTemplate}
                disabled={sendNotificationMutation.isPending || isRefreshing}
                className="border-blue-300 text-blue-700 hover:bg-blue-100"
              >
                <Eye className="h-3 w-3 mr-2" />
                Preview Template
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={sendNotificationMutation.isPending || isRefreshing}
                className="border-green-300 text-green-700 hover:bg-green-100"
              >
                {isRefreshing ? (
                  <>
                    <Loader2 className="h-3 w-3 mr-2 animate-spin" />
                    Refreshing...
                  </>
                ) : (
                  <>
                    <RefreshCw className="h-3 w-3 mr-2" />
                    Refresh
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Email Template Preview Modal - Always available */}
      <Dialog open={isPreviewModalOpen} onOpenChange={setIsPreviewModalOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5" />
              Email Template Preview
              {pendingNotification && (
                <Badge variant="secondary" className="ml-2">
                  {applicationNumber || pendingNotification.applicationNumber}
                </Badge>
              )}
            </DialogTitle>
          </DialogHeader>

          {pendingNotification ? (
            <div className="space-y-4">
              {/* Email Subject */}
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">
                  Subject:
                </h4>
                <p className="text-sm bg-gray-50 p-3 rounded border">
                  {pendingNotification.emailPreview.subject}
                </p>
              </div>

              {/* Email Content */}
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">
                  Email Content:
                </h4>
                <div
                  className="bg-white border rounded p-4 max-h-96 overflow-y-auto"
                  dangerouslySetInnerHTML={{
                    __html: pendingNotification.emailPreview.htmlContent,
                  }}
                />
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center p-8">
              <div className="text-center">
                <AlertCircle className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-600">
                  No email template data available to preview
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  Please refresh the notification data and try again
                </p>
              </div>
            </div>
          )}

          <div className="flex justify-end pt-4">
            <DialogClose asChild>
              <Button variant="outline" onClick={handleClosePreview}>
                Close Preview
              </Button>
            </DialogClose>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};
