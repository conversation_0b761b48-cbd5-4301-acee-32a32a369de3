import React from "react";

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/dialog";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { serviceSchema } from "@/utils/schema";
import { useCreateService, useUpdateService } from "@/hooks/use-query";
import ButtonLoader from "../ui/button-loader";
import { Textarea } from "../ui/textarea";

interface IServiceFormProp {
  id: string;
  service: IService | undefined;
}

const ServiceForm: React.FC<IServiceFormProp> = ({ id, service }) => {
  const form = useForm<z.infer<typeof serviceSchema>>({
    resolver: zod<PERSON><PERSON><PERSON><PERSON>(serviceSchema),
    defaultValues: {
      name: service?.name,
      price: service?.price,
      meeting_link: service?.meeting_link,
      description: service?.description,
    },
  });
  const { mutate: create, isPending: isCreating } = useCreateService(id);
  const { mutate: update, isPending: isUpdating } = useUpdateService(
    service?.id || ""
  );
  const onSubmit = (data: z.infer<typeof serviceSchema>) => {
    if (service) {
      update(data);
    } else {
      create(data);
    }
  };
  return (
    <DialogContent className="max-w-3xl">
      <DialogHeader>
        <DialogTitle>
          {service ? "Edit Service" : "Add New Service"}
        </DialogTitle>
      </DialogHeader>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className=" space-y-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Service Name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="price"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Price</FormLabel>
                  <FormControl>
                    <div className="relative flex rounded-lg shadow-sm shadow-black/5">
                      <span className="pointer-events-none absolute inset-y-0 start-0 flex items-center justify-center ps-3 text-sm text-muted-foreground">
                        €
                      </span>
                      <Input
                        id="input-16"
                        className="-me-px rounded-e-none ps-6 shadow-none"
                        placeholder="0.00"
                        type="number"
                        {...field}
                      />
                      <span className="-z-10 inline-flex items-center rounded-e-lg border border-input bg-background px-3 text-sm text-muted-foreground">
                        EUR
                      </span>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="meeting_link"
              render={({ field }) => (
                <FormItem className="col-span-2">
                  <FormLabel>Meeting Url</FormLabel>
                  <FormControl>
                    <div className="flex rounded-lg shadow-sm shadow-black/5">
                      <span className="-z-10 inline-flex items-center rounded-s-lg border border-input bg-background px-3 text-sm text-muted-foreground">
                        https://
                      </span>
                      <Input
                        id="input-14"
                        className="-ms-px rounded-s-none shadow-none"
                        placeholder="google.com"
                        type="text"
                        {...field}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem className="col-span-2">
                  <FormLabel>About</FormLabel>
                  <FormControl>
                    <Textarea
                      id="textarea-07"
                      className="border-transparent bg-muted shadow-none"
                      placeholder="Leave a information about service"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          {isCreating || isUpdating ? (
            <ButtonLoader />
          ) : (
            <Button>Submit</Button>
          )}
        </form>
      </Form>
    </DialogContent>
  );
};

export default ServiceForm;
