"use client";

import React from "react";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useDeleteAgent } from "@/hooks/use-query";

interface DeleteAgentDialogProps {
  agent: IAgent;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export const DeleteAgentDialog: React.FC<DeleteAgentDialogProps> = ({
  agent,
  open,
  onOpenChange,
  onSuccess,
}) => {
  const deleteAgentMutation = useDeleteAgent();

  const handleDelete = async () => {
    try {
      await deleteAgentMutation.mutateAsync(agent.id!);
      onSuccess?.();
    } catch (error) {
      // Error handling is done in the mutation
      console.error("Failed to delete agent:", error);
    }
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Delete Agent</AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to delete the agent &quot;{agent.name}&quot;?
            This action cannot be undone and will remove the agent from all
            assigned applications.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={deleteAgentMutation.isPending}>
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={deleteAgentMutation.isPending}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {deleteAgentMutation.isPending ? "Deleting..." : "Delete"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
