"use client";

import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";

import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON>alogHeader,
  <PERSON>alogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { useUpdateWorkflowMaster } from "@/hooks/use-query";
import { workflowMasterSchema } from "@/utils/schema";

interface EditWorkflowMasterDialogProps {
  workflow: IWorkflowMaster | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

type FormData = z.infer<typeof workflowMasterSchema>;

export const EditWorkflowMasterDialog: React.FC<
  EditWorkflowMasterDialogProps
> = ({ workflow, open, onOpenChange, onSuccess }) => {
  const updateMutation = useUpdateWorkflowMaster();

  const form = useForm<FormData>({
    resolver: zodResolver(workflowMasterSchema),
    defaultValues: {
      name: "",
      description: "",
      is_active: true,
    },
  });

  // Reset form when workflow changes or dialog closes
  useEffect(() => {
    if (workflow && open) {
      form.reset({
        name: workflow.name || "",
        description: workflow.description || "",
        is_active: workflow.is_active ?? true,
      });
    } else if (!open) {
      // Clear form state when closing
      form.reset();
    }
  }, [workflow, open, form]);

  const onSubmit = async (data: FormData) => {
    if (!workflow?.id) return;

    try {
      await updateMutation.mutateAsync({
        id: workflow.id,
        ...data,
      });
      form.reset();
      onOpenChange(false);
      onSuccess();
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  if (!workflow) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Edit Workflow Master</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Workflow Name *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="e.g., Document Review Process, Approval Workflow"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Brief description of the workflow process"
                      rows={4}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="is_active"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Active Status</FormLabel>
                    <div className="text-sm text-muted-foreground">
                      Enable this workflow for use in the system
                    </div>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <div className="flex justify-end space-x-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={updateMutation.isPending}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={updateMutation.isPending}>
                {updateMutation.isPending ? "Updating..." : "Update Workflow"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};
