import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "../ui/badge";
import { Briefcase } from "lucide-react";
import { NoResults } from "@/loader/no-results";
import Progress from "../common/progress";

const Immigration = ({ data }: { data: ImmigrationService[] }) => {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <CardTitle className="text-xl font-bold flex items-center gap-2">
          <Briefcase className="h-5 w-5" />
          Immigration Services
        </CardTitle>
      </CardHeader>
      <CardContent>
        {data.length === 0 ? (
          <NoResults
            title=""
            description="There are No purchase by this user for this service"
          />
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Service</TableHead>
                <TableHead>Amount</TableHead>
                <TableHead>Payment Status</TableHead>
                <TableHead>Progress</TableHead>
                <TableHead>Date</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.map((el) => (
                <TableRow key={el.id}>
                  <TableCell>{el.immigration_service.name}</TableCell>
                  <TableCell>€{el.amount}</TableCell>
                  <TableCell>
                    <Badge variant="success" className="capitalize">
                      {el.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Progress
                      id={el.id}
                      type="immigration"
                      status={el.progress}
                    />
                  </TableCell>
                  <TableCell className="text-muted-foreground">
                    {new Date(el.createdAt).toLocaleDateString()}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  );
};

export default Immigration;
