"use client";
import React from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON><PERSON><PERSON><PERSON> } from "../ui/dialog";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { userSchema } from "@/utils/schema";
import { useCreateUser, useUpdateUser } from "@/hooks/use-query";
import ButtonLoader from "../ui/button-loader";

interface IUserProp {
  user: IUser | undefined;
}

const UserForm: React.FC<IUserProp> = ({ user }) => {
  const form = useForm<z.infer<typeof userSchema>>({
    resolver: zodResolver(userSchema),
    defaultValues: {
      name: user?.name,
      email: user?.email,
    },
  });
  const { mutate: create, isPending: isCreating } = useCreateUser();
  const { mutate: update, isPending: isUpdating } = useUpdateUser(
    user?.id as string
  );

  const onSubmit = (data: z.infer<typeof userSchema>) => {
    if (user) {
      update(data);
    } else {
      create(data);
    }
  };
  return (
    <DialogContent className="max-w-lg max-h-[60vh] overflow-y-scroll scrollbar scrollbar-w-0">
      <DialogHeader>
        <DialogTitle>{user ? "Edit User" : "Add New user"}</DialogTitle>
      </DialogHeader>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className=" space-y-4">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem className="lg:col-span-2">
                <FormLabel>Username</FormLabel>
                <FormControl>
                  <Input placeholder="John" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem className="lg:col-span-2">
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input placeholder="<EMAIL>" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem className="lg:col-span-2">
                <FormLabel>Password</FormLabel>
                <FormControl>
                  <Input
                    placeholder={
                      user ? "password can only be updated" : "***************"
                    }
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          {isCreating || isUpdating ? (
            <ButtonLoader />
          ) : (
            <Button>Submit</Button>
          )}
        </form>
      </Form>
    </DialogContent>
  );
};

export default UserForm;
