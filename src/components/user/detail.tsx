import React from "react";
import { Card, CardContent } from "../ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { imgUrl } from "@/utils/urls";
import { isValidUrl } from "@/utils/tools";
import { Badge } from "../ui/badge";
import { CalendarDays, CheckCircle, Mail, XCircle } from "lucide-react";

const Detail = ({ data }: { data: IUser }) => {
  return (
    <Card className="p-0">
      <CardContent className="flex flex-col justify-center items-center p-2 gap-4">
        <Avatar className="w-full h-[200px] lg:w-[200px] rounded-md bg-[#f8f8f8] dark:bg-[#18181b]">
          <AvatarImage
            src={
              isValidUrl(data.image || "")
                ? `${data.image}`
                : `${imgUrl}${data.image}`
            }
            alt={data.name}
            className=" rounded-md object-contain"
          />
          <AvatarFallback>
            {data.name.substring(0, 2).toUpperCase()}
          </AvatarFallback>
        </Avatar>
        <div className="space-y-2 flex flex-col justify-center items-center">
          <h2 className="text-2xl font-semibold capitalize">{data.name}</h2>
          <Badge variant="secondary" className="capitalize">
            {data.provider}
          </Badge>
        </div>

        <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
          <Mail className="h-4 w-4" />
          <span>{data.email}</span>
        </div>
        <div className="flex items-center justify-center gap-1 text-sm">
          {data.emailVerified ? (
            <>
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span className="text-green-500">Email verified</span>
            </>
          ) : (
            <>
              <XCircle className="h-4 w-4 text-red-500" />
              <span className="text-red-500">Email not verified</span>
            </>
          )}
        </div>
        <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
          <CalendarDays className="h-4 w-4" />
          <span>
            Joined{" "}
            {new Date(data.createdAt).toLocaleDateString("en-US", {
              year: "numeric",
              month: "long",
              day: "numeric",
            })}
          </span>
        </div>
      </CardContent>
    </Card>
  );
};

export default Detail;
