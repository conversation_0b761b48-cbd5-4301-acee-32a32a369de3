"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { loginSchema, agentLoginSchema } from "@/utils/schema";
import { useSearchParams } from "next/navigation";
import { signIn } from "next-auth/react";
import { Shield, Users } from "lucide-react";

const EnhancedLoginForm = () => {
  const params = useSearchParams();
  const error = params.get("error");
  const provider = params.get("provider");
  const [activeTab, setActiveTab] = useState("admin");
  const [isLoading, setIsLoading] = useState(false);

  // Set the correct tab based on the provider parameter from failed authentication
  useEffect(() => {
    if (provider === "agent") {
      setActiveTab("agent");
    } else if (provider === "admin") {
      setActiveTab("admin");
    }
    // Default to admin tab if no provider specified
  }, [provider]);

  // Admin login form
  const adminForm = useForm<z.infer<typeof loginSchema>>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  // Agent login form
  const agentForm = useForm<z.infer<typeof agentLoginSchema>>({
    resolver: zodResolver(agentLoginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const onAdminSubmit = async (data: z.infer<typeof loginSchema>) => {
    setIsLoading(true);
    try {
      await signIn("credentials", {
        email: data.email,
        password: data.password,
        callbackUrl: "/",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const onAgentSubmit = async (data: z.infer<typeof agentLoginSchema>) => {
    setIsLoading(true);
    try {
      await signIn("agent-credentials", {
        email: data.email,
        password: data.password,
        callbackUrl: "/applications",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="w-full">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="admin" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Admin Login
          </TabsTrigger>
          <TabsTrigger value="agent" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Agent Login
          </TabsTrigger>
        </TabsList>

        <TabsContent value="admin" className="mt-6">
          <Form {...adminForm}>
            <form
              onSubmit={adminForm.handleSubmit(onAdminSubmit)}
              className="grid gap-4"
            >
              {error &&
                activeTab === "admin" &&
                (provider === "admin" || !provider) && (
                  <div className="bg-red-500 p-2 rounded-md text-white text-center font-semibold">
                    {error}
                  </div>
                )}

              <FormField
                control={adminForm.control}
                name="email"
                render={({ field }) => (
                  <FormItem className="grid gap-2">
                    <FormLabel>Admin Email</FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="<EMAIL>"
                        {...field}
                        autoComplete="current-email"
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={adminForm.control}
                name="password"
                render={({ field }) => (
                  <FormItem className="grid gap-2">
                    <div className="flex items-center justify-between">
                      <FormLabel>Password</FormLabel>
                      <Link
                        href="/admin/forgot-password"
                        className="text-sm text-muted-foreground hover:text-primary underline-offset-4 hover:underline"
                      >
                        Forgot Password?
                      </Link>
                    </div>
                    <FormControl>
                      <Input
                        type="password"
                        {...field}
                        autoComplete="current-password"
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? "Signing in..." : "Sign in as Admin"}
              </Button>
            </form>
          </Form>
        </TabsContent>

        <TabsContent value="agent" className="mt-6">
          <Form {...agentForm}>
            <form
              onSubmit={agentForm.handleSubmit(onAgentSubmit)}
              className="grid gap-4"
            >
              {error && activeTab === "agent" && provider === "agent" && (
                <div className="bg-red-500 p-2 rounded-md text-white text-center font-semibold">
                  {error}
                </div>
              )}

              <FormField
                control={agentForm.control}
                name="email"
                render={({ field }) => (
                  <FormItem className="grid gap-2">
                    <FormLabel>Agent Email</FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="<EMAIL>"
                        {...field}
                        autoComplete="current-email"
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={agentForm.control}
                name="password"
                render={({ field }) => (
                  <FormItem className="grid gap-2">
                    <div className="flex items-center justify-between">
                      <FormLabel>Password</FormLabel>
                      <Link
                        href="/agent/forgot-password"
                        className="text-sm text-muted-foreground hover:text-primary underline-offset-4 hover:underline"
                      >
                        Forgot Password?
                      </Link>
                    </div>
                    <FormControl>
                      <Input
                        type="password"
                        {...field}
                        autoComplete="current-password"
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? "Signing in..." : "Sign in as Agent"}
              </Button>
            </form>
          </Form>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default EnhancedLoginForm;
