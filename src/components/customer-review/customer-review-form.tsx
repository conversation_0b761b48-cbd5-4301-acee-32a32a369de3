"use client";
import React from "react";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { customerReviewSchema } from "@/utils/schema";
import {
  useCreateCustomerReview,
  useUpdateCustomerReview,
} from "@/hooks/use-query";
import ButtonLoader from "../ui/button-loader";
import FileUpload from "../common/file-upload";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useId } from "react";
import { StarIcon } from "lucide-react";
import { Textarea } from "../ui/textarea";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { DropdownNavProps, DropdownProps } from "react-day-picker";
interface ICustomerReviewProp {
  customer: ICustomerReview | undefined;
}
const CustomerReviewForm: React.FC<ICustomerReviewProp> = ({ customer }) => {
  const id = useId();
  const [hoverRating, setHoverRating] = React.useState("");

  const form = useForm<z.infer<typeof customerReviewSchema>>({
    resolver: zodResolver(customerReviewSchema),
    defaultValues: {
      ...customer,
      date: customer ? new Date(customer.date) : new Date(),
    },
  });
  const { mutate: create, isPending: isCreating } = useCreateCustomerReview();
  const { mutate: update, isPending: isUpdating } = useUpdateCustomerReview(
    customer?.id || ""
  );
  const onSubmit = (data: z.infer<typeof customerReviewSchema>) => {
    if (customer) {
      update(data);
    } else {
      create(data);
    }
  };

  const handleCalendarChange = (
    _value: string | number,
    _e: React.ChangeEventHandler<HTMLSelectElement>
  ) => {
    const _event = {
      target: {
        value: String(_value),
      },
    } as React.ChangeEvent<HTMLSelectElement>;
    _e(_event);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className=" space-y-8">
        <FileUpload
          form={form}
          field="img"
          message="PNG,JPEG and WEBP are allowed"
          accept=".png,.jpeg,.webp"
          folder="customer-review"
        />
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Name</FormLabel>
                <FormControl>
                  <Input placeholder="User Name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="source"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Source</FormLabel>
                <FormControl>
                  <Input placeholder="Linkedin" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="order"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Order</FormLabel>
                <FormControl>
                  <Input type="number" min={1} placeholder="1" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="date"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Date</FormLabel>
                <FormControl>
                  <div>
                    <div className="*:not-first:mt-2">
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            id={id}
                            variant={"outline"}
                            className={cn(
                              "group bg-background hover:bg-background border-input w-full justify-between px-3 font-normal outline-offset-0 outline-none focus-visible:outline-[3px]",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            <span
                              className={cn(
                                "truncate",
                                !field.value && "text-muted-foreground"
                              )}
                            >
                              {field.value
                                ? format(field.value, "PPP")
                                : "Pick a date"}
                            </span>
                            <CalendarIcon
                              size={16}
                              className="text-muted-foreground/80 group-hover:text-foreground shrink-0 transition-colors"
                              aria-hidden="true"
                            />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto" align="center">
                          <div>
                            <Calendar
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              className="mx-0 "
                              captionLayout="dropdown"
                              defaultMonth={new Date()}
                              startMonth={new Date(1980, 6)}
                              hideNavigation
                              components={{
                                DropdownNav: (props: DropdownNavProps) => {
                                  return (
                                    <div className="flex w-full items-center gap-2">
                                      {props.children}
                                    </div>
                                  );
                                },
                                Dropdown: (props: DropdownProps) => {
                                  return (
                                    <Select
                                      value={String(props.value)}
                                      onValueChange={(value) => {
                                        if (props.onChange) {
                                          handleCalendarChange(
                                            value,
                                            props.onChange
                                          );
                                        }
                                      }}
                                    >
                                      <SelectTrigger className="h-8 w-fit font-medium first:grow">
                                        <SelectValue />
                                      </SelectTrigger>
                                      <SelectContent className="max-h-[min(26rem,var(--radix-select-content-available-height))]">
                                        {props.options?.map((option) => (
                                          <SelectItem
                                            key={option.value}
                                            value={String(option.value)}
                                            disabled={option.disabled}
                                          >
                                            {option.label}
                                          </SelectItem>
                                        ))}
                                      </SelectContent>
                                    </Select>
                                  );
                                },
                              }}
                            />
                          </div>
                        </PopoverContent>
                      </Popover>
                    </div>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="rating"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Rating</FormLabel>
                <FormControl>
                  <fieldset className="space-y-4">
                    <RadioGroup
                      className="inline-flex gap-0"
                      onValueChange={(value) => field.onChange(Number(value))}
                    >
                      {["1", "2", "3", "4", "5"].map((value) => (
                        <label
                          key={value}
                          className="group focus-within:border-ring focus-within:ring-ring/50 relative cursor-pointer rounded p-0.5 outline-none focus-within:ring-[3px]"
                          onMouseEnter={() => setHoverRating(value)}
                          onMouseLeave={() => setHoverRating("")}
                        >
                          <RadioGroupItem
                            id={`${id}-${value}`}
                            value={value}
                            className="sr-only"
                          />
                          <StarIcon
                            size={24}
                            className={`transition-all ${
                              (hoverRating || field?.value?.toString()) >= value
                                ? "fill-amber-500 text-amber-500"
                                : "text-input"
                            } group-hover:scale-110`}
                          />
                          <span className="sr-only">
                            {value} star{value === "1" ? "" : "s"}
                          </span>
                        </label>
                      ))}
                    </RadioGroup>
                  </fieldset>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <FormField
          control={form.control}
          name="comment"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Comment</FormLabel>
              <FormControl>
                <div className="*:not-first:mt-2">
                  <Textarea
                    {...field}
                    className="read-only:bg-muted"
                    rows={8}
                    placeholder="Leave a comment"
                  />
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        {isCreating || isUpdating ? <ButtonLoader /> : <Button>Submit</Button>}
      </form>
    </Form>
  );
};

export default CustomerReviewForm;
