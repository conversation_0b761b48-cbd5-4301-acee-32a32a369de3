import { useRemoveCustomerReview } from "@/hooks/use-query";
import { TrashIcon } from "lucide-react";
import React from "react";
import { Button } from "../ui/button";

const DeleteCustomer = ({ id }: { id: string }) => {
  const { mutate } = useRemoveCustomerReview();
  return (
    <Button
      variant="none"
      className="bg-[#ffebe6] text-[#fd381d]"
      onClick={() => mutate(id)}
      size="icon"
      aria-label="Add new item"
    >
      <TrashIcon size={16} strokeWidth={2} aria-hidden="true" />
    </Button>
  );
};

export default DeleteCustomer;
