"use client";

import React from "react";
import { ColumnDef } from "@tanstack/react-table";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { format } from "date-fns";
import { imgUrl } from "@/utils/urls";
import { isValidUrl } from "@/utils/tools";
import Link from "next/link";
import { Edit, EyeIcon } from "lucide-react";
import DeleteUser from "@/components/user/delete-user";
import UserForm from "@/components/user/user-form";
import { Dialog, DialogTrigger } from "@/components/ui/dialog";

export const userUsColumns: ColumnDef<IUser>[] = [
  {
    id: "Image",
    cell: ({ row }) => {
      return (
        <Avatar className="w-16 h-16">
          <AvatarImage
            src={
              isValidUrl(row.original.image || "")
                ? `${row.original.image}`
                : `${imgUrl}${row.original.image}`
            }
            alt={row.original.name}
          />
          <AvatarFallback>{row.original.name.charAt(0)}</AvatarFallback>
        </Avatar>
      );
    },
  },
  {
    accessorKey: "name",
    header: "Name",
  },
  {
    accessorKey: "email",
    header: "Email",
  },
  {
    accessorKey: "provider",
    header: "Provider",
  },
  {
    id: "Created At",
    header: () => "Created At",
    cell: ({ row }) => {
      return (
        <p>
          {format(new Date(row.original.createdAt as Date), "PPP 'at' HH:mm")}
        </p>
      );
    },
  },
  {
    id: "Action",
    header: () => "",
    cell: ({ row }) => {
      return (
        <div className="flex  items-center justify-start space-x-3">
          <Link
            href={`/user/${row.original.id}`}
            className="bg-[#e7f6f1] text-[#16a87e] p-2 rounded-lg"
          >
            <EyeIcon className="w-5 h-5  " />
          </Link>
          <Dialog>
            <DialogTrigger className="bg-[#404BD0]/10 text-[#404BD0] p-2 rounded-lg">
              <Edit className="w-5 h-5  " />
            </DialogTrigger>
            <UserForm user={row.original} />
          </Dialog>
          <DeleteUser id={row.original.id as string} />
        </div>
      );
    },
  },
];
