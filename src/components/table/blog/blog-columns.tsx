"use client";

import React from "react";
import { ColumnDef } from "@tanstack/react-table";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { imgUrl } from "@/utils/urls";
import { EyeIcon, MessageSquare } from "lucide-react";
import Link from "next/link";
import { format } from "date-fns";
import DeleteBlog from "@/components/blog/delete-blog";

export const blogColumns: ColumnDef<IBlog>[] = [
  {
    id: "Image",
    cell: ({ row }) => {
      return (
        <Avatar className="w-20 h-20 rounded-md bg-[#fafafa] dark:bg-[#18181b]">
          <AvatarImage
            className="rounded-none object-contain"
            src={imgUrl + row.original.img}
          />
          <AvatarFallback>CN</AvatarFallback>
        </Avatar>
      );
    },
  },
  {
    accessorKey: "title",
    header: "Title",
  },
  {
    accessorKey: "blogger",
    header: "Author",
  },
  {
    id: "Created At",
    header: () => "Create At",
    cell: ({ row }) => {
      return (
        <p>
          {format(new Date(row.original.createdAt as Date), "PPP 'at' HH:mm")}
        </p>
      );
    },
  },
  {
    id: "Action",
    header: () => "",
    cell: ({ row }) => {
      return (
        <div className="flex  items-center justify-start space-x-3">
          <Link
            href={`/blog/${row.original.slug}`}
            className="bg-[#e7f6f1] text-[#16a87e] p-2 rounded-lg"
          >
            <EyeIcon className="w-5 h-5  " />
          </Link>
          <Link
            href={`/blog/comments?id=${row.original.id}`}
            className="bg-[#404BD0]/10 text-[#404BD0] p-2 rounded-lg"
          >
            <MessageSquare className="w-5 h-5  " />
          </Link>
          <DeleteBlog id={row.original.id as string} />
        </div>
      );
    },
  },
];
