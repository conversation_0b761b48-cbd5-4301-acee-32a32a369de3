"use client";

import React from "react";
import { ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";
import { Badge } from "@/components/ui/badge";
import GuestProgress from "@/components/common/guest-progress";

export const guestColumns: ColumnDef<IGuest>[] = [
  {
    accessorKey: "name",
    header: "Name",
  },
  {
    accessorKey: "email",
    header: "Email",
  },
  {
    accessorKey: "mobile_no",
    header: "Mobile",
  },
  {
    id: "amount",
    header: "Amount",
    cell: ({ row }) => <p>€{row.original.amount}</p>,
  },
  {
    id: "payment-status",
    header: "Payment Status",
    cell: ({ row }) => (
      <Badge variant="success" className="capitalize">
        {row.original.status}
      </Badge>
    ),
  },

  {
    id: "Created At",
    header: () => "",
    cell: ({ row }) => {
      return <p>{format(new Date(row.original.createdAt), "PP")}</p>;
    },
  },
];

export const guestServices: ColumnDef<IGuestService>[] = [
  {
    id: "service",
    header: "Service",
    cell: ({ row }) => (
      <div>
        <p className="text-base font-semibold">
          {row.original?.mentor_services.name}
        </p>
        <p className="text-muted-foreground">
          {row.original?.mentor_services?.mentor?.name}
        </p>
      </div>
    ),
  },
  {
    id: "progress",
    header: "Progress",
    cell: ({ row }) => (
      <GuestProgress
        id={row.original.id}
        type="guest-service"
        status={row.original.progress}
        validate="guest-services"
      />
    ),
  },

  //  @ts-ignore
  ...guestColumns,
];
export const guestPackages: ColumnDef<IGuestPackage>[] = [
  {
    id: "service",
    header: "Service",
    cell: ({ row }) => (
      <div>
        <p>{row.original?.package.name}</p>
      </div>
    ),
  },
  {
    id: "progress",
    header: "Progress",
    cell: ({ row }) => (
      <GuestProgress
        id={row.original.id}
        type="guest-package"
        status={row.original.progress}
        validate="guest-packages"
      />
    ),
  },

  //  @ts-ignore
  ...guestColumns,
];
export const guestImmigration: ColumnDef<IGuestImmigration>[] = [
  {
    id: "service",
    header: "Service",
    cell: ({ row }) => (
      <div>
        <p>{row.original?.immigration_service.name}</p>
      </div>
    ),
  },
  {
    id: "progress",
    header: "Progress",
    cell: ({ row }) => (
      <GuestProgress
        id={row.original.id}
        type="guest-immigration"
        status={row.original.progress}
        validate="guest-immigration"
      />
    ),
  },

  //  @ts-ignore
  ...guestColumns,
];
export const guestTraining: ColumnDef<IGuestTraining>[] = [
  {
    id: "service",
    header: "Service",
    cell: ({ row }) => (
      <div>
        <p>{row.original?.training.name}</p>
      </div>
    ),
  },
  {
    id: "progress",
    header: "Progress",
    cell: ({ row }) => (
      <GuestProgress
        id={row.original.id}
        type="guest-training"
        status={row.original.progress}
        validate="guest-training"
      />
    ),
  },

  //  @ts-ignore
  ...guestColumns,
];
