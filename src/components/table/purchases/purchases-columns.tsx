"use client";
import React from "react";
import { ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { format } from "date-fns";
import { EditableProgressCell } from "./editable-progress-cell";

export const createPurchasesColumns = (
  onProgressUpdate: (paymentId: string, newProgress: string) => Promise<void>
): ColumnDef<PaymentData>[] => [
  {
    accessorKey: "id",
    header: "Payment ID",
    cell: ({ row }) => (
      <div className="font-mono text-sm">
        {row.original.id.substring(0, 8)}...
      </div>
    ),
  },
  {
    id: "customer",
    header: "Customer",
    cell: ({ row }) => {
      const payment = row.original;

      if (payment.payment_type === "user" && payment.user) {
        return (
          <div className="flex items-center gap-3">
            <Avatar className="h-8 w-8">
              <AvatarImage
                src={payment.user.image || ""}
                alt={payment.user.name}
              />
              <AvatarFallback className="bg-muted">
                {payment.user.name.substring(0, 2).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="flex flex-col">
              <span className="font-medium capitalize">
                {payment.user.name}
              </span>
              <span className="text-sm text-muted-foreground">
                {payment.user.email}
              </span>
            </div>
          </div>
        );
      } else {
        return (
          <div className="flex flex-col">
            <span className="font-medium capitalize">
              {payment.guest_name || "Guest User"}
            </span>
            <span className="text-sm text-muted-foreground">
              {payment.guest_email}
            </span>
            {payment.guest_mobile && (
              <span className="text-xs text-muted-foreground">
                {payment.guest_mobile}
              </span>
            )}
          </div>
        );
      }
    },
  },
  {
    id: "service",
    header: "Service",
    cell: ({ row }) => {
      return (
        <div className="flex flex-col">
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs capitalize">
              {row.original.service_type}
            </Badge>
          </div>
        </div>
      );
    },
  },
  {
    id: "amount",
    header: "Amount",
    cell: ({ row }) => (
      <div className="font-medium">€{row.original.amount}</div>
    ),
  },
  {
    id: "payment-status",
    header: "Payment Status",
    cell: ({ row }) => {
      const status = row.original.status;
      let variant: "default" | "success" | "destructive" | "secondary" =
        "default";

      switch (status) {
        case "paid":
          variant = "success";
          break;
        case "failed":
          variant = "destructive";
          break;
        case "pending":
          variant = "secondary";
          break;
      }

      return (
        <Badge variant={variant} className="capitalize">
          {status}
        </Badge>
      );
    },
  },
  {
    id: "payment-type",
    header: "Payment Type",
    cell: ({ row }) => (
      <Badge variant="outline" className="capitalize">
        {row.original.payment_type}
      </Badge>
    ),
  },
  {
    id: "progress",
    header: "Progress",
    cell: ({ row }) => {
      const payment = row.original;
      return (
        <EditableProgressCell
          paymentId={payment.id}
          currentProgress={payment.progress}
          onProgressUpdate={onProgressUpdate}
        />
      );
    },
  },
  {
    id: "createdAt",
    header: "Date",
    cell: ({ row }) => (
      <div className="text-sm">
        {format(new Date(row.original.createdAt), "PP")}
      </div>
    ),
  },
];

// Default columns without progress editing (for backward compatibility)
export const purchasesColumns: ColumnDef<PaymentData>[] =
  createPurchasesColumns(async () => {
    console.warn("Progress update handler not provided");
  });
