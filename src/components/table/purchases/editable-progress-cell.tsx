"use client";

import React, { useState } from "react";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Check, X, Loader2 } from "lucide-react";

interface EditableProgressCellProps {
  paymentId: string;
  currentProgress: string;
  onProgressUpdate: (paymentId: string, newProgress: string) => Promise<void>;
}

const progressOptions = [
  { value: "Accepted", label: "Accepted", variant: "success" as const },
  { value: "Rejected", label: "Rejected", variant: "destructive" as const },
  { value: "Pending", label: "Pending", variant: "secondary" as const },
  { value: "Completed", label: "Completed", variant: "success" as const },
  { value: "Active", label: "Active", variant: "success" as const },
  { value: "Inactive", label: "Inactive", variant: "secondary" as const },
  { value: "Blocked", label: "Blocked", variant: "destructive" as const },
  { value: "Cancelled", label: "Cancelled", variant: "destructive" as const },
];

export function EditableProgressCell({
  paymentId,
  currentProgress,
  onProgressUpdate,
}: EditableProgressCellProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [selectedProgress, setSelectedProgress] = useState(currentProgress);
  const [isUpdating, setIsUpdating] = useState(false);

  const currentOption = progressOptions.find(
    (option) => option.value === currentProgress
  );

  const handleEdit = () => {
    setIsEditing(true);
    setSelectedProgress(currentProgress);
  };

  const handleCancel = () => {
    setIsEditing(false);
    setSelectedProgress(currentProgress);
  };

  const handleSave = async () => {
    if (selectedProgress === currentProgress) {
      setIsEditing(false);
      return;
    }

    setIsUpdating(true);
    try {
      await onProgressUpdate(paymentId, selectedProgress);
      setIsEditing(false);
    } catch (error) {
      // Error handling is done in the parent component
      console.error("Failed to update progress:", error);
    } finally {
      setIsUpdating(false);
    }
  };

  if (isEditing) {
    return (
      <div className="flex items-center gap-2">
        <Select value={selectedProgress} onValueChange={setSelectedProgress}>
          <SelectTrigger className="w-[140px] h-8">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {progressOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <div className="flex gap-1">
          <Button
            size="sm"
            variant="ghost"
            className="h-8 w-8 p-0"
            onClick={handleSave}
            disabled={isUpdating}
          >
            {isUpdating ? (
              <Loader2 className="h-3 w-3 animate-spin" />
            ) : (
              <Check className="h-3 w-3" />
            )}
          </Button>
          <Button
            size="sm"
            variant="ghost"
            className="h-8 w-8 p-0"
            onClick={handleCancel}
            disabled={isUpdating}
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      </div>
    );
  }

  return (
    <Badge
      variant={currentOption?.variant || "outline"}
      className="cursor-pointer hover:opacity-80 transition-opacity capitalize"
      onClick={handleEdit}
    >
      {currentOption?.label || currentProgress}
    </Badge>
  );
}
