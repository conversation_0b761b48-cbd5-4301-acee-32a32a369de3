"use client";
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { zod<PERSON><PERSON>ol<PERSON> } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { mentorSchema } from "@/utils/schema";
import FileUpload from "../common/file-upload";
import TextEditor from "../common/text-editor";
import { useCreateMentor, useUpdateMentor } from "@/hooks/use-query";
import ButtonLoader from "../ui/button-loader";

interface IMentorFormProp {
  mentor: IMentor | undefined;
}

const MentorForm: React.FC<IMentorFormProp> = ({ mentor }) => {
  const form = useForm<z.infer<typeof mentorSchema>>({
    resolver: zodResolver(mentorSchema),
    defaultValues: {
      ...mentor,
      order: mentor
        ? mentor.order === null
          ? undefined
          : mentor.order
        : undefined,
      linkedin: mentor?.linkedin === null ? undefined : mentor?.linkedin,
      profile: mentor?.profile === null ? undefined : mentor?.profile,
    },
  });

  const { mutate: create, isPending: isCreating } = useCreateMentor();
  const { mutate: update, isPending: isUpdating } = useUpdateMentor(
    mentor?.id || ""
  );
  const onSubmit = (data: z.infer<typeof mentorSchema>) => {
    if (mentor) {
      update(data);
    } else {
      create(data);
    }
  };
  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className=" space-y-8">
        <FileUpload
          form={form}
          field="image"
          message="PNG,JPEG and WEBP are allowed"
          accept=".png,.jpeg,.webp"
          folder="mentor"
        />
        <div className="grid grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Name</FormLabel>
                <FormControl>
                  <Input placeholder="Mentor Name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input placeholder="Mentor Email" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="designation"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Designation</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Data Engineer in SSE Renewables"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Password</FormLabel>
                <FormControl>
                  <Input placeholder="password" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="order"
            render={({ field }) => (
              <FormItem className=" ">
                <FormLabel>Order</FormLabel>
                <FormControl>
                  <Input placeholder="1" min={1} type="number" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="linkedin"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Linkedin Profile Url</FormLabel>
                <FormControl>
                  <Input placeholder="https://www.linkedin.com" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="profile"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Training Courses</FormLabel>
                <FormControl>
                  <Input placeholder="other mentor link " {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <FormField
          control={form.control}
          name="desc"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <TextEditor field={field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        {isCreating || isUpdating ? (
          <ButtonLoader />
        ) : (
          <Button type="submit">Submit</Button>
        )}
      </form>
    </Form>
  );
};

export default MentorForm;
