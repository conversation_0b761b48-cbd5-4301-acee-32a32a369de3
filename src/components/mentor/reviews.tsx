"use client";
import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { NoResults } from "@/loader/no-results";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { isValidUrl } from "@/utils/tools";
import { imgUrl } from "@/utils/urls";
import { <PERSON>R<PERSON>, Star } from "lucide-react";
import { Button } from "../ui/button";
import { useRouter } from "next/navigation";

const Reviews = ({ data }: { data: IMentorInfo }) => {
  const router = useRouter();
  return (
    <Card>
      <CardHeader className="font-bold flex flex-row items-center justify-between">
        <p className="text-xl">Reviews</p>
      </CardHeader>
      <CardContent className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {data.reviews.length === 0 && (
          <NoResults
            title="There are No reviews for this mentor"
            description="Wait for user to rate this mentor"
            className="col-span-3"
          />
        )}

        {data.reviews.map((el) => (
          <Card key={el.id} className="p-0 w-full">
            <CardContent className="p-3 lg:p-6">
              <div className="flex items-center gap-4 mb-4">
                <Avatar className="w-10 h-10  rounded-full bg-[#f8f8f8] dark:bg-[#18181b] cursor-pointer">
                  <AvatarImage
                    src={
                      isValidUrl(el.user?.image || "")
                        ? `${el.user?.image}`
                        : `${imgUrl}${el.user?.image}`
                    }
                    alt={el?.user?.name}
                    className=" rounded-md object-contain"
                  />
                  <AvatarFallback>
                    {el?.user?.name?.substring(0, 2).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <h3 className="font-medium  capitalize">{el?.user?.name}</h3>
                  <div className="flex gap-1">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <Star
                        key={star}
                        className={`w-5 h-5 ${
                          star <= el.rating
                            ? "fill-yellow-400 text-yellow-400"
                            : "text-gray-300"
                        }`}
                      />
                    ))}
                  </div>
                </div>
              </div>
              <p className="mt-3 ">{el.message}</p>
              <div className="mt-2 text-sm text-muted-foreground">
                {new Date(el.createdAt).toLocaleDateString("en-US", {
                  year: "numeric",
                  month: "long",
                  day: "numeric",
                })}
              </div>
            </CardContent>
            <CardFooter>
              <Button
                className="group"
                onClick={() => router.push(`/user/${el.user.id}`)}
              >
                View User
                <ArrowRight
                  className="-me-1 ms-2 opacity-60 transition-transform group-hover:translate-x-0.5"
                  size={16}
                  strokeWidth={2}
                  aria-hidden="true"
                />
              </Button>
            </CardFooter>
          </Card>
        ))}
      </CardContent>
    </Card>
  );
};

export default Reviews;
