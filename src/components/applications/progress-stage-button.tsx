"use client";

import React from "react";
import { Loader2 } from "lucide-react";
import { useProgressStage, useProfile } from "@/hooks/use-query";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface ProgressStageSelectProps {
  applicationId: string;
  currentStep: number;
  numberOfSteps?: number;
  steps: IApplicationStep[];
  className?: string;
}

export const ProgressStageSelect: React.FC<ProgressStageSelectProps> = ({
  applicationId,
  currentStep,
  numberOfSteps,
  steps,
  className = "",
}) => {
  const { data: profileData } = useProfile();
  const progressStageMutation = useProgressStage(applicationId);

  // Check if user has permission (both admin and agent can progress stages)
  const userRole = profileData?.data?.tokenType;
  const hasPermission = userRole === "admin" || userRole === "agent";

  // Get all available stages for bidirectional navigation
  const getAvailableStages = () => {
    if (!steps || steps.length === 0) return [];

    // Return ALL stages to allow full bidirectional navigation
    return steps.sort((a, b) => a.stageOrder - b.stageOrder);
  };

  const availableStages = getAvailableStages();

  const handleStageSelect = (selectedStageOrder: string) => {
    const stageOrderNumber = parseInt(selectedStageOrder);

    // Basic validation - only check if it's a valid number
    if (isNaN(stageOrderNumber) || stageOrderNumber < 1) {
      return;
    }

    // Allow bidirectional navigation - no restrictions on stage selection
    progressStageMutation.mutate({ currentStep: selectedStageOrder });
  };

  // Don't render if user doesn't have permission
  if (!hasPermission) {
    return null;
  }

  // Don't render if no stages available
  if (availableStages.length === 0) {
    return null;
  }

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <span className="text-sm font-medium text-muted-foreground">
        Progress to:
      </span>
      <Select
        value={currentStep.toString()}
        onValueChange={handleStageSelect}
        disabled={progressStageMutation.isPending}
      >
        <SelectTrigger className="w-[200px] bg-gradient-to-r from-green-50 to-emerald-50 border-green-200 hover:from-green-100 hover:to-emerald-100 transition-all duration-200">
          <SelectValue>
            {progressStageMutation.isPending ? (
              <div className="flex items-center">
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </div>
            ) : (
              steps.find((step) => step.stageOrder === currentStep)
                ?.stageName || `Stage ${currentStep}`
            )}
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          {availableStages.map((stage) => (
            <SelectItem
              key={stage.stageOrder}
              value={stage.stageOrder.toString()}
              className="cursor-pointer"
            >
              <div className="flex items-center justify-between w-full">
                <span>{stage.stageName}</span>
                <span className="text-xs text-muted-foreground ml-2">
                  Stage {stage.stageOrder}
                </span>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};
