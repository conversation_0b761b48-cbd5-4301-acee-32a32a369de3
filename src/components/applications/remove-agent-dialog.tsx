"use client";

import React from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { AlertTriangle, Loader2 } from "lucide-react";
import { useRemoveAgent } from "@/hooks/use-query";

interface RemoveAgentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  application: IApplication;
  agent: IAgent;
  onSuccess?: () => void;
}

export const RemoveAgentDialog: React.FC<RemoveAgentDialogProps> = ({
  open,
  onOpenChange,
  application,
  agent,
  onSuccess,
}) => {
  const removeAgentMutation = useRemoveAgent();

  const handleRemove = async () => {
    if (!agent.id) return;

    try {
      await removeAgentMutation.mutateAsync({
        applicationId: application.id,
        agentId: agent.id,
      });
      onSuccess?.();
      onOpenChange(false);
    } catch (error) {
      // Error handling is done in the mutation
      console.error("Failed to remove agent:", error);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!removeAgentMutation.isPending) {
      onOpenChange(newOpen);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-destructive" />
            Remove Agent
          </DialogTitle>
          <DialogDescription className="text-left">
            Are you sure you want to remove{" "}
            <span className="font-semibold">{agent.name}</span> from application{" "}
            <span className="font-semibold">
              {application.application_number}
            </span>
            ?
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <div className="bg-muted/50 p-4 rounded-lg space-y-2">
            <div className="text-sm">
              <span className="font-medium">Agent:</span> {agent.name} (
              {agent.email})
            </div>
            <div className="text-sm">
              <span className="font-medium">Application:</span>{" "}
              {application.application_number}
            </div>
            <div className="text-sm">
              <span className="font-medium">Service:</span>{" "}
              {application.service_name || application.service_type}
            </div>
          </div>

          <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p className="text-sm text-yellow-800">
              <strong>Note:</strong> This action will remove the agent&apos;s
              assignment from this application. The agent will no longer have
              access to manage this application&apos;s workflow.
            </p>
          </div>
        </div>

        <DialogFooter className="flex gap-2">
          <Button
            type="button"
            variant="outline"
            onClick={() => handleOpenChange(false)}
            disabled={removeAgentMutation.isPending}
          >
            Cancel
          </Button>

          <Button
            variant="destructive"
            onClick={handleRemove}
            disabled={removeAgentMutation.isPending}
          >
            {removeAgentMutation.isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Removing...
              </>
            ) : (
              "Remove Agent"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
