"use client";

import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Loader2, User, AlertCircle } from "lucide-react";
import { toast } from "sonner";

import { Alert, AlertDescription } from "@/components/ui/alert";

const userSelectionSchema = z
  .object({
    selectionType: z.enum(["existing", "new"]),
    userId: z.string().optional(),
    newUser: z
      .object({
        name: z.string().optional(),
        email: z.string().optional(),
        phone: z.string().optional(),
      })
      .optional(),
  })
  .superRefine((data, ctx) => {
    // Validate existing user selection
    if (data.selectionType === "existing") {
      if (!data.userId || data.userId.trim() === "") {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Please select a user",
          path: ["userId"],
        });
      }
    }

    // Validate new user creation
    if (data.selectionType === "new") {
      if (!data.newUser) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "User details are required",
          path: ["newUser"],
        });
        return;
      }

      // Validate name
      if (!data.newUser.name || data.newUser.name.trim() === "") {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Name is required",
          path: ["newUser", "name"],
        });
      }

      // Validate email
      if (!data.newUser.email || data.newUser.email.trim() === "") {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Email is required",
          path: ["newUser", "email"],
        });
      } else {
        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(data.newUser.email)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Please enter a valid email address",
            path: ["newUser", "email"],
          });
        }
      }

      // Validate phone
      if (!data.newUser.phone || data.newUser.phone.trim() === "") {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Phone number is required",
          path: ["newUser", "phone"],
        });
      }
    }
  });

type FormData = z.infer<typeof userSelectionSchema>;

interface UserSelectionFormProps {
  onNext: (data: {
    userId?: string;
    createNewUser: boolean;
    newUser?: any;
  }) => void;
  initialData?: {
    userId?: string;
    createNewUser: boolean;
    newUser?: any;
  };
}

interface IUser {
  id: string;
  name: string;
  email: string;
  phone?: string;
}

export const UserSelectionForm: React.FC<UserSelectionFormProps> = ({
  onNext,
  initialData,
}) => {
  const [users, setUsers] = useState<IUser[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingUsers, setLoadingUsers] = useState(true);
  const [usersError, setUsersError] = useState<string | null>(null);
  const [apiError, setApiError] = useState<string | null>(null);

  const form = useForm<FormData>({
    resolver: zodResolver(userSelectionSchema),
    defaultValues: {
      selectionType: initialData?.createNewUser ? "new" : "existing",
      userId: initialData?.userId || "",
      newUser: initialData?.newUser || {
        name: "",
        email: "",
        phone: "",
      },
    },
  });

  const selectionType = form.watch("selectionType");
  const userId = form.watch("userId");
  const newUser = form.watch("newUser");

  // Check if form is valid for submission
  const isFormValid = () => {
    if (selectionType === "existing") {
      return userId && userId.trim() !== "";
    } else if (selectionType === "new") {
      return (
        newUser &&
        newUser.name &&
        newUser.name.trim() !== "" &&
        newUser.email &&
        newUser.email.trim() !== "" &&
        newUser.phone &&
        newUser.phone.trim() !== "" &&
        /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(newUser.email)
      );
    }
    return false;
  };

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setLoadingUsers(true);
        setUsersError(null);
        const response = await fetch("/api/user/admin");
        if (response.ok) {
          const data = await response.json();
          setUsers(data.data || data || []);
        } else {
          throw new Error("Failed to fetch users");
        }
      } catch (error) {
        console.error("Error fetching users:", error);
        setUsersError(
          error instanceof Error ? error.message : "Failed to load users"
        );
      } finally {
        setLoadingUsers(false);
      }
    };

    fetchUsers();
  }, []);

  // Reset form fields when selection type changes
  useEffect(() => {
    // Clear API error when switching selection types
    setApiError(null);

    if (selectionType === "existing") {
      // Clear new user fields when switching to existing
      form.setValue("newUser", undefined);
      // Clear validation errors for new user fields
      form.clearErrors("newUser");
      form.clearErrors("newUser.name");
      form.clearErrors("newUser.email");
      form.clearErrors("newUser.phone");
      // Clear root validation error
      form.clearErrors();
    } else if (selectionType === "new") {
      // Clear user ID when switching to new
      form.setValue("userId", "");
      // Initialize newUser object if it doesn't exist
      if (!form.getValues("newUser")) {
        form.setValue("newUser", {
          name: "",
          email: "",
          phone: "",
        });
      }
      // Clear validation errors for user ID
      form.clearErrors("userId");
      // Clear root validation error
      form.clearErrors();
    }
  }, [selectionType, form]);

  const onSubmit = async (data: FormData) => {
    setLoading(true);
    setApiError(null);

    try {
      if (data.selectionType === "existing") {
        // Handle existing user selection
        onNext({
          userId: data.userId,
          createNewUser: false,
          newUser: undefined,
        });
      } else if (data.selectionType === "new" && data.newUser) {
        // Handle new user creation - call API to register user

        const response = await fetch("/api/user/register", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            name: data.newUser.name,
            email: data.newUser.email,
            mobileNo: data.newUser.phone, // Map phone to mobileNo for the API
          }),
        });

        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.message || "Failed to register user");
        }

        // Enhanced user ID extraction with better error handling
        let userId = null;

        // Try multiple possible response structures
        if (result.data?.id) {
          userId = result.data.id;
        } else if (result.user?.id) {
          userId = result.user.id;
        } else if (result.id) {
          userId = result.id;
        } else if (result.data?.user?.id) {
          userId = result.data.user.id;
        }

        if (!userId) {
          throw new Error(
            "User registration succeeded but no user ID was returned. Please contact support."
          );
        }

        // Validate that userId is a string and not empty
        if (typeof userId !== "string" || userId.trim() === "") {
          throw new Error(
            "Invalid user ID format received. Please try again or contact support."
          );
        }

        // Show success message
        toast.success("User registered successfully", {
          description:
            "The user has been created and can now be used in the application",
        });

        // Pass the newly created user ID to the next step
        onNext({
          userId: userId,
          createNewUser: true,
          newUser: data.newUser,
        });
      }
    } catch (error: any) {
      // Enhanced error handling with more specific messages
      let errorMessage = "An unexpected error occurred";

      if (error.message) {
        errorMessage = error.message;
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (typeof error === "string") {
        errorMessage = error;
      }

      setApiError(errorMessage);

      toast.error("Failed to process user selection", {
        description: errorMessage,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5" />
          User Selection
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {apiError && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{apiError}</AlertDescription>
              </Alert>
            )}
            <FormField
              control={form.control}
              name="selectionType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Choose User Option</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="flex flex-col space-y-2"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="existing" id="existing" />
                        <Label htmlFor="existing">Select Existing User</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="new" id="new" />
                        <Label htmlFor="new">Create New User</Label>
                      </div>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {selectionType === "existing" && (
              <>
                {usersError && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{usersError}</AlertDescription>
                  </Alert>
                )}
                <FormField
                  control={form.control}
                  name="userId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Select User <span className="text-red-500">*</span>
                      </FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                        disabled={loadingUsers || !!usersError}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue
                              placeholder={
                                loadingUsers
                                  ? "Loading users..."
                                  : usersError
                                    ? "Error loading users"
                                    : users.length === 0
                                      ? "No users available"
                                      : "Select a user"
                              }
                            />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {users.map((user) => (
                            <SelectItem key={user.id} value={user.id}>
                              {user.name} ({user.email})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </>
            )}

            {selectionType === "new" && (
              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="newUser.name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Name <span className="text-red-500">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input placeholder="Enter user name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="newUser.email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Email <span className="text-red-500">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder="Enter email address"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="newUser.phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Phone <span className="text-red-500">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input placeholder="Enter phone number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}

            <Button
              type="submit"
              disabled={loading || !isFormValid()}
              className="w-full"
            >
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Continue to Product Selection
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};
