"use client";

import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, Calculator, ArrowLeft } from "lucide-react";

const discountSchema = z
  .object({
    originalPrice: z.coerce.number().min(0, "Original price must be positive"),
    discountAmount: z.coerce
      .number()
      .min(0, "Discount must be positive")
      .optional(),
    discountedPrice: z.coerce.number().min(0, "Final price must be positive"),
  })
  .refine(
    (data) => {
      if (data.discountAmount && data.discountAmount > data.originalPrice) {
        return false;
      }
      return true;
    },
    {
      message: "Discount cannot exceed original price",
      path: ["discountAmount"],
    }
  );

type FormData = z.infer<typeof discountSchema>;

interface DiscountApplicationFormProps {
  onNext: (data: {
    originalPrice: number;
    discountAmount?: number;
    discountedPrice: number;
  }) => void;
  onBack: () => void;
  initialData: {
    originalPrice: number;
    discountAmount?: number;
    discountedPrice?: number;
  };
}

export const DiscountApplicationForm: React.FC<
  DiscountApplicationFormProps
> = ({ onNext, onBack, initialData }) => {
  const [loading, setLoading] = useState(false);

  const form = useForm<FormData>({
    resolver: zodResolver(discountSchema),
    defaultValues: {
      originalPrice: initialData.originalPrice,
      discountAmount: initialData.discountAmount || 0,
      discountedPrice: initialData.discountedPrice || initialData.originalPrice,
    },
  });

  const originalPrice = form.watch("originalPrice");
  const discountAmount = form.watch("discountAmount");

  // Auto-calculate discounted price when discount amount changes
  useEffect(() => {
    if (discountAmount !== undefined) {
      const finalPrice = Math.max(0, originalPrice - discountAmount);
      form.setValue("discountedPrice", finalPrice);
    }
  }, [discountAmount, originalPrice, form]);

  // Validate discount amount doesn't exceed original price
  useEffect(() => {
    if (discountAmount && discountAmount > originalPrice) {
      form.setError("discountAmount", {
        type: "manual",
        message: "Discount cannot exceed original price",
      });
    } else {
      form.clearErrors("discountAmount");
    }
  }, [discountAmount, originalPrice, form]);

  const onSubmit = async (data: FormData) => {
    setLoading(true);
    try {
      onNext({
        originalPrice: data.originalPrice,
        discountAmount: data.discountAmount || 0,
        discountedPrice: data.discountedPrice,
      });
    } finally {
      setLoading(false);
    }
  };

  const discountPercentage =
    discountAmount && originalPrice > 0
      ? ((discountAmount / originalPrice) * 100).toFixed(1)
      : "0";

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calculator className="h-5 w-5" />
          Discount Application
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="originalPrice"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Original Price</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="Original price"
                      disabled
                      {...field}
                      className="bg-muted"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="discountAmount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Discount Amount (Optional)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="Enter discount amount"
                      {...field}
                      onChange={(e) => {
                        const value = e.target.value;
                        // If the field currently has 0 and user types a number, replace the 0
                        if (
                          field.value === 0 &&
                          value &&
                          !value.startsWith("0")
                        ) {
                          field.onChange(parseFloat(value) || 0);
                        } else {
                          field.onChange(parseFloat(value) || 0);
                        }
                      }}
                      onFocus={(e) => {
                        // Select all text when focused if the value is 0
                        if (field.value === 0) {
                          e.target.select();
                        }
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                  {discountAmount && discountAmount > 0 && (
                    <p className="text-sm text-muted-foreground">
                      Discount: {discountPercentage}% off
                    </p>
                  )}
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="discountedPrice"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Final Price</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="Final price"
                      disabled
                      {...field}
                      className="bg-muted font-semibold"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="p-4 bg-primary/5 rounded-lg border">
              <h4 className="font-medium mb-2">Price Summary</h4>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span>Original Price:</span>
                  <span>${originalPrice.toFixed(2)}</span>
                </div>
                {discountAmount && discountAmount > 0 && (
                  <div className="flex justify-between text-red-600">
                    <span>Discount:</span>
                    <span>-${discountAmount.toFixed(2)}</span>
                  </div>
                )}
                <div className="flex justify-between font-semibold border-t pt-1">
                  <span>Final Price:</span>
                  <span>
                    ${(originalPrice - (discountAmount || 0)).toFixed(2)}
                  </span>
                </div>
              </div>
            </div>

            <div className="flex gap-4">
              <Button
                type="button"
                variant="outline"
                onClick={onBack}
                className="flex-1"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back
              </Button>
              <Button type="submit" disabled={loading} className="flex-1">
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Continue to Workflow
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};
