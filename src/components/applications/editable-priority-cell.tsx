"use client";

import React, { useState } from "react";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";

interface EditablePriorityCellProps {
  applicationId: string;
  currentPriority: string;
  onPriorityUpdate: (
    applicationId: string,
    newPriority: string
  ) => Promise<void>;
}

const priorityOptions = [
  { value: "Low", label: "Low" },
  { value: "Medium", label: "Medium" },
  { value: "High", label: "High" },
  { value: "Critical", label: "Critical" },
];

const getPriorityBadgeVariant = (priority: string) => {
  switch (priority.toLowerCase()) {
    case "high":
      return "destructive";
    case "medium":
      return "secondary";
    case "low":
      return "outline";
    case "critical":
      return "destructive";
    default:
      return "outline";
  }
};

export const EditablePriorityCell: React.FC<EditablePriorityCellProps> = ({
  applicationId,
  currentPriority,
  onPriorityUpdate,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  const handleEdit = () => {
    setIsEditing(true);
  };

  /**
   * Handle priority change with immediate update
   * No confirmation buttons - updates immediately on selection
   * @param {string} newPriority - The new priority value
   */
  const handlePriorityChange = async (newPriority: string) => {
    if (newPriority === currentPriority) {
      setIsEditing(false);
      return;
    }

    setIsUpdating(true);
    try {
      await onPriorityUpdate(applicationId, newPriority);
      setIsEditing(false);
      toast.success(`Priority level updated to ${newPriority} successfully`);
    } catch (error) {
      console.error("Failed to update priority:", error);

      // Provide user-friendly error message
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to update priority level";

      toast.error(errorMessage);
      // Keep editing mode open so user can try again
    } finally {
      setIsUpdating(false);
    }
  };

  // Show loading state during update
  if (isUpdating) {
    return (
      <div className="flex items-center gap-2 px-3 py-1">
        <Loader2 className="h-3 w-3 animate-spin" />
        <span className="text-sm text-muted-foreground">Updating...</span>
      </div>
    );
  }

  // Render editing mode with dropdown
  if (isEditing) {
    return (
      <Select
        value={currentPriority}
        onValueChange={handlePriorityChange}
        onOpenChange={(open) => {
          if (!open) {
            // Close editing mode when dropdown closes without selection
            setIsEditing(false);
          }
        }}
        open={true} // Keep dropdown open while editing
      >
        <SelectTrigger className="w-[120px] h-8">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          {priorityOptions.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    );
  }

  return (
    <Badge
      variant={getPriorityBadgeVariant(currentPriority)}
      className="capitalize cursor-pointer hover:opacity-80"
      onClick={handleEdit}
    >
      {currentPriority}
    </Badge>
  );
};
