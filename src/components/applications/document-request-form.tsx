"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useRequestDocument } from "@/hooks/use-query";
import { Plus, Loader2 } from "lucide-react";

interface DocumentRequestFormProps {
  applicationId: string;
  currentStageOrder: number;
  onDocumentRequested?: () => void;
}

// Form validation schema
const formSchema = z.object({
  documentName: z.string().min(1, "Document name is required"),
  reason: z.string().min(1, "Reason is required"),
  documentCategory: z.string().min(1, "Document category is required"),
  stageOrder: z.number().min(1, "Stage order must be at least 1"),
  required: z.boolean(),
});

type FormData = z.infer<typeof formSchema>;

// Common document categories
const documentCategories = [
  "Identity Documents",
  "Educational Certificates",
  "Work Experience",
  "Financial Documents",
  "Legal Documents",
  "Medical Records",
  "Immigration Documents",
  "Supporting Documents",
  "Other",
];

export const DocumentRequestForm: React.FC<DocumentRequestFormProps> = ({
  applicationId,
  currentStageOrder,
  onDocumentRequested,
}) => {
  const [open, setOpen] = useState(false);
  const requestDocumentMutation = useRequestDocument(applicationId);

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      documentName: "",
      reason: "",
      documentCategory: "",
      stageOrder: currentStageOrder,
      required: true,
    },
  });

  const onSubmit = async (data: FormData) => {
    try {
      await requestDocumentMutation.mutateAsync(data);

      // Reset form and close dialog
      form.reset({
        documentName: "",
        reason: "",
        documentCategory: "",
        stageOrder: currentStageOrder,
        required: true,
      });
      setOpen(false);
      onDocumentRequested?.();
    } catch (error) {
      // Error is handled by the mutation hook
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="gap-2">
          <Plus className="h-4 w-4" />
          Request Document
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Request New Document</DialogTitle>
          <DialogDescription>
            Request a new document from the applicant for this application.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="documentName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Document Name <span className="text-destructive">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="e.g., Updated Passport Copy"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="documentCategory"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Document Category{" "}
                    <span className="text-destructive">*</span>
                  </FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a category" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {documentCategories.map((category) => (
                        <SelectItem key={category} value={category}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="stageOrder"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Stage Order <span className="text-destructive">*</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min="1"
                      placeholder="1"
                      {...field}
                      onChange={(e) =>
                        field.onChange(parseInt(e.target.value) || 1)
                      }
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="required"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">
                      Required Document
                    </FormLabel>
                    <div className="text-sm text-muted-foreground">
                      Mark this document as required for application completion
                    </div>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="reason"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Reason for Request{" "}
                    <span className="text-destructive">*</span>
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Please explain why this document is needed..."
                      className="min-h-[80px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}
                disabled={requestDocumentMutation.isPending}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={requestDocumentMutation.isPending}
              >
                {requestDocumentMutation.isPending && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                Submit Request
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};
