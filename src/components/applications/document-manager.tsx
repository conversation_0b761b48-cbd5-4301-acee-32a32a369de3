"use client";

import React, { useState, useEffect, use<PERSON>em<PERSON> } from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Upload,
  FileText,
  Download,
  CheckCircle,
  AlertCircle,
  RefreshCw,
  Trash2,
} from "lucide-react";
import {
  useUploadApplicationDocument,
  useDeleteApplicationDocument,
} from "@/hooks/use-query";
import { imgUrl } from "@/utils/urls";
import { DocumentStatusManager } from "./document-status-manager";
import { DocumentRequestForm } from "./document-request-form";
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  <PERSON><PERSON>D<PERSON>og<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  AlertDialog<PERSON><PERSON><PERSON>,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

interface DocumentManagerProps {
  documents: IWorkflowDocument[] | IApplicationDocumentData[];
  uploadedDocuments: IApplicationDocument[];
  onDocumentUpload: (documents: IApplicationDocument[]) => void;
  onDocumentStatusUpdate?: (
    documentId: string,
    newStatus: DocumentStatus,
    reason?: string
  ) => void;
  onDocumentDelete?: (documentId: string) => void;
  applicationId: string;
  stageName: string;
  stageOrder: number;
}

// Helper function to normalize document data
const normalizeDocument = (
  doc: IWorkflowDocument | IApplicationDocumentData
): { name: string; required: boolean; id?: string } => {
  if ("documentName" in doc) {
    // Legacy format
    return { name: doc.documentName, required: doc.required };
  } else {
    // New format
    return { name: doc.fileName, required: doc.required, id: doc.id };
  }
};

export const DocumentManager: React.FC<DocumentManagerProps> = ({
  documents,
  uploadedDocuments,
  onDocumentUpload,
  onDocumentStatusUpdate,
  onDocumentDelete,
  applicationId,
  stageName,
  stageOrder,
}) => {
  const [uploadingDocs, setUploadingDocs] = useState<Set<string>>(new Set());
  const [deletingDocs, setDeletingDocs] = useState<Set<string>>(new Set());
  // Local state to track uploaded documents for immediate UI updates
  const [localUploadedDocs, setLocalUploadedDocs] = useState<
    IApplicationDocument[]
  >([]);
  // State to track recently uploaded documents for success animation
  const [recentlyUploaded, setRecentlyUploaded] = useState<Set<string>>(
    new Set()
  );
  const uploadDocumentMutation = useUploadApplicationDocument(applicationId);
  const deleteDocumentMutation = useDeleteApplicationDocument(applicationId);

  // Sync local state with parent state to ensure consistency
  // This ensures that when parent state updates (e.g., from server refresh),
  // local state stays in sync while maintaining immediate UI updates
  useEffect(() => {
    setLocalUploadedDocs(uploadedDocuments);
  }, [uploadedDocuments]);

  // Combine parent uploaded documents with local state for immediate UI updates
  // This provides instant feedback while parent state propagates through the component tree
  const effectiveUploadedDocuments = useMemo(() => {
    const combined = [...localUploadedDocs];

    // Add any documents from parent that aren't in local state
    // This handles cases where documents are added from other sources
    uploadedDocuments.forEach((parentDoc) => {
      const existsInLocal = combined.some(
        (localDoc) =>
          localDoc.document_name === parentDoc.document_name &&
          localDoc.stage_name === parentDoc.stage_name
      );
      if (!existsInLocal) {
        combined.push(parentDoc);
      }
    });

    return combined;
  }, [localUploadedDocs, uploadedDocuments]);

  /**
   * Validates backend upload response structure
   * @param {any} result - The backend response to validate
   * @return {string | null} - Error message if validation fails, null if valid
   */
  const validateUploadResponse = (result: any): string | null => {
    if (!result.data) {
      return "Invalid response format: missing data field";
    }
    if (!result.data.document_id) {
      return "Document uploaded but no document_id returned from backend";
    }
    if (!result.data.file_path) {
      return "Document uploaded but no file_path returned from backend";
    }
    return null;
  };

  /**
   * Validates file before upload
   * @param {File} file - The file to validate
   * @return {string | null} - Error message if validation fails, null if valid
   */
  const validateFile = (file: File): string | null => {
    // Check file size (20MB limit)
    const maxSize = 20 * 1024 * 1024; // 20MB in bytes
    if (file.size > maxSize) {
      return "File size exceeds 20MB limit";
    }

    // Check file type
    const allowedTypes = [
      "application/pdf",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      "image/jpeg",
      "image/jpg",
      "image/png",
    ];

    if (!allowedTypes.includes(file.type)) {
      return "Invalid file format. Please upload PDF, DOC, DOCX, JPG, JPEG, or PNG files only";
    }

    return null;
  };

  const handleFileUpload = async (
    file: File,
    documentName: string,
    required: boolean,
    documentId?: string
  ) => {
    // Validate file before upload
    const validationError = validateFile(file);
    if (validationError) {
      toast.error("Invalid file", {
        description: validationError,
      });
      return;
    }

    setUploadingDocs((prev) => new Set(prev).add(documentName));

    try {
      // Use the new document upload API
      const result = await uploadDocumentMutation.mutateAsync({
        file,
        document_name: documentName,
        stage_order: stageOrder,
        document_id: documentId,
      });

      if (result.success) {
        // Validate backend response structure using helper function
        const validationError = validateUploadResponse(result);
        if (validationError) {
          console.error("Backend response validation failed:", result);
          throw new Error(validationError);
        }

        // Create a new document object for immediate UI update
        // Map backend response fields to frontend interface
        // Backend response structure: { success: true, data: { document_id, file_path, upload_date, status } }
        const newDocument: IApplicationDocument = {
          id: result.data.document_id, // Backend field: document_id (not 'id')
          document_name: documentName,
          file_url: result.data.file_path, // Backend field: file_path (not 'file_url')
          stage_name: stageName,
          uploaded_at: result.data.upload_date || new Date().toISOString(), // Backend field: upload_date (not 'uploaded_at')
          uploaded_by: result.data.uploaded_by || "admin",
          status: result.data.status || "Pending",
          reason: result.data.reason,
        };

        // Update local state immediately for instant UI feedback
        // This ensures the upload interface disappears immediately without waiting for parent state
        setLocalUploadedDocs((prev) => {
          const updated = [...prev];
          // Check if this is a re-upload (document with same name and stage)
          const existingIndex = updated.findIndex(
            (doc) =>
              doc.document_name === documentName && doc.stage_name === stageName
          );

          if (existingIndex !== -1) {
            // Replace existing document (re-upload scenario)
            updated[existingIndex] = newDocument;
          } else {
            // Add new document (first upload scenario)
            updated.push(newDocument);
          }
          return updated;
        });

        // Add success animation feedback for visual confirmation
        setRecentlyUploaded((prev) => new Set(prev).add(documentName));

        // Remove success animation after 3 seconds for clean UI
        setTimeout(() => {
          setRecentlyUploaded((prev) => {
            const newSet = new Set(prev);
            newSet.delete(documentName);
            return newSet;
          });
        }, 3000);

        // Also notify parent component for global state management
        // This ensures other components and server state stay in sync
        onDocumentUpload([newDocument]);
      } else {
        throw new Error(result.message || "Upload failed");
      }
    } catch (error) {
      console.error("Error uploading document:", error);

      // Provide user-friendly error feedback with specific handling for response parsing issues
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error occurred";

      if (errorMessage.includes("document_id")) {
        toast.error("Upload response error", {
          description:
            "Document uploaded but server response is invalid. Please refresh and try again.",
        });
      } else if (errorMessage.includes("file_path")) {
        toast.error("Upload response error", {
          description:
            "Document uploaded but file path is missing. Please contact support.",
        });
      } else if (errorMessage.includes("Invalid response format")) {
        toast.error("Server response error", {
          description:
            "Invalid response from server. Please try again or contact support.",
        });
      } else if (errorMessage.includes("size")) {
        toast.error("File too large", {
          description: "Please select a file smaller than 20MB",
        });
      } else if (
        errorMessage.includes("format") ||
        errorMessage.includes("type")
      ) {
        toast.error("Invalid file format", {
          description:
            "Please upload PDF, DOC, DOCX, JPG, JPEG, or PNG files only",
        });
      } else if (
        errorMessage.includes("network") ||
        errorMessage.includes("connection")
      ) {
        toast.error("Network error", {
          description: "Please check your internet connection and try again",
        });
      } else {
        // Generic error message if no specific error type is detected
        toast.error("Upload failed", {
          description: errorMessage || "Please try again later",
        });
      }
    } finally {
      setUploadingDocs((prev) => {
        const newSet = new Set(prev);
        newSet.delete(documentName);
        return newSet;
      });
    }
  };

  const handleFileInputChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    documentName: string,
    required: boolean,
    documentId?: string
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type and size
      const maxSize = 10 * 1024 * 1024; // 10MB
      const allowedTypes = [
        "application/pdf",
        "image/jpeg",
        "image/jpg",
        "image/png",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      ];

      if (!allowedTypes.includes(file.type)) {
        toast.error("Invalid file type", {
          description: "Please upload PDF, Word document, or image files only.",
        });
        return;
      }

      if (file.size > maxSize) {
        toast.error("File too large", {
          description: "Please upload files smaller than 10MB.",
        });
        return;
      }
      handleFileUpload(file, documentName, required, documentId);
    }
    // Reset input value to allow re-uploading the same file
    event.target.value = "";
  };

  const getDocumentStatus = (documentName: string, required: boolean) => {
    // Use effective uploaded documents for immediate UI updates
    const uploadedDoc = effectiveUploadedDocuments.find(
      (doc) => doc.document_name === documentName
    );

    if (uploadedDoc) {
      return {
        status: "uploaded",
        document: uploadedDoc,
        variant: "default" as const,
        icon: CheckCircle,
        label: "Uploaded",
      };
    }

    if (required) {
      return {
        status: "required",
        document: null,
        variant: "destructive" as const,
        icon: AlertCircle,
        label: "Required",
      };
    }

    return {
      status: "optional",
      document: null,
      variant: "secondary" as const,
      icon: FileText,
      label: "Optional",
    };
  };

  /**
   * Handles document deletion with proper state management and user feedback
   * @param {string} documentId - The ID of the document to delete
   */
  const handleDocumentDelete = async (documentId: string) => {
    setDeletingDocs((prev) => new Set(prev).add(documentId));

    try {
      const result = await deleteDocumentMutation.mutateAsync(documentId);

      if (result.success) {
        // Update local state immediately for instant UI feedback
        setLocalUploadedDocs((prev) =>
          prev.filter((doc) => doc.id !== documentId)
        );

        // Also notify parent component for global state management
        onDocumentDelete?.(documentId);
      } else {
        throw new Error(result.message || "Delete failed");
      }
    } catch (error) {
      console.error("Error deleting document:", error);
      // Error handling is already done by the mutation hook
    } finally {
      setDeletingDocs((prev) => {
        const newSet = new Set(prev);
        newSet.delete(documentId);
        return newSet;
      });
    }
  };

  const handleDownload = (applicationDocument: IApplicationDocument) => {
    const link = document.createElement("a");
    link.href = imgUrl + applicationDocument.file_url;
    link.download = applicationDocument.document_name;
    link.target = "_blank";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (documents.length === 0) {
    return (
      <div className="text-center py-8">
        <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <p className="text-muted-foreground">
          No documents required for this stage.
        </p>
      </div>
    );
  }

  // Preserve the exact order from backend response - no client-side sorting
  const sortedDocuments = documents;

  return (
    <div className="space-y-3">
      {/* Consolidated Documents Card */}
      <Card className="overflow-hidden">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-2 text-lg">
            <FileText className="h-5 w-5" />
            Documents ({effectiveUploadedDocuments.length}/{documents.length})
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            Upload required documents to proceed with your application
          </p>
        </CardHeader>
        <CardContent className="space-y-3">
          {sortedDocuments.map((doc) => {
            const normalizedDoc = normalizeDocument(doc);
            const status = getDocumentStatus(
              normalizedDoc.name,
              normalizedDoc.required
            );
            const isUploading = uploadingDocs.has(normalizedDoc.name);

            return (
              <div
                key={normalizedDoc.name}
                className="group relative p-3 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 transition-all duration-200 bg-white dark:bg-gray-900 hover:shadow-sm"
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <div
                      className={`p-1.5 rounded-md transition-transform duration-200 group-hover:scale-105 ${
                        status.status === "uploaded"
                          ? "bg-green-500 text-white"
                          : status.status === "required"
                            ? "bg-red-500 text-white"
                            : "bg-gray-500 text-white"
                      }`}
                    >
                      <status.icon className="h-3 w-3" />
                    </div>
                    <div className="min-w-0 flex-1">
                      <h4 className="font-medium text-sm text-gray-900 dark:text-gray-100 truncate">
                        {normalizedDoc.name}
                      </h4>
                      {normalizedDoc.required && (
                        <span className="text-xs text-red-600 dark:text-red-400 font-medium">
                          Required
                        </span>
                      )}
                    </div>
                  </div>
                  <Badge
                    variant={status.variant}
                    className="flex items-center gap-1 text-xs shrink-0"
                  >
                    {status.label}
                  </Badge>
                </div>
                {status.document ? (
                  <div className="space-y-3">
                    <div
                      className={`flex items-center justify-between p-2 bg-green-50 dark:bg-green-950/20 rounded-md border border-green-200 dark:border-green-800 transition-all duration-300 ${
                        recentlyUploaded.has(normalizedDoc.name)
                          ? "ring-2 ring-green-400 ring-opacity-75 shadow-lg scale-[1.02]"
                          : ""
                      }`}
                    >
                      <div className="flex items-center gap-2 min-w-0 flex-1">
                        <div className="p-1.5 bg-green-500 text-white rounded-md">
                          <CheckCircle className="h-3 w-3" />
                        </div>
                        <div className="min-w-0 flex-1">
                          <p className="font-medium text-sm text-green-900 dark:text-green-100 truncate">
                            {status.document.document_name}
                          </p>
                          <p className="text-xs text-green-700 dark:text-green-300">
                            {new Date(
                              status.document.uploaded_at
                            ).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      <div className="flex gap-1 shrink-0">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDownload(status.document!)}
                          className="h-7 w-7 p-0 hover:bg-green-100 dark:hover:bg-green-900/20"
                          title="Download"
                        >
                          <Download className="h-3 w-3" />
                        </Button>
                        {/* Re-upload button */}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const fileInput = document.getElementById(
                              `reupload-${normalizedDoc.name}`
                            ) as HTMLInputElement;
                            fileInput?.click();
                          }}
                          className="h-7 w-7 p-0 hover:bg-blue-100 dark:hover:bg-blue-900/20 text-blue-600 hover:text-blue-700"
                          title="Re-upload"
                          disabled={isUploading}
                        >
                          <Upload className="h-3 w-3" />
                        </Button>
                        {/* Delete button with confirmation dialog */}
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              className="h-7 w-7 p-0 hover:bg-red-100 dark:hover:bg-red-900/20 text-red-600 hover:text-red-700"
                              title="Delete document"
                              disabled={deletingDocs.has(status.document.id)}
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>
                                Delete Document
                              </AlertDialogTitle>
                              <AlertDialogDescription>
                                Are you sure you want to delete &ldquo;
                                {status.document.document_name}&rdquo;? This
                                action cannot be undone and the file will be
                                permanently removed.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() =>
                                  handleDocumentDelete(status.document.id)
                                }
                                className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
                                disabled={deletingDocs.has(status.document.id)}
                              >
                                {deletingDocs.has(status.document.id)
                                  ? "Deleting..."
                                  : "Delete"}
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                      {/* Hidden file input for re-upload */}
                      <input
                        id={`reupload-${normalizedDoc.name}`}
                        type="file"
                        accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                        style={{ display: "none" }}
                        onChange={(e) =>
                          handleFileInputChange(
                            e,
                            normalizedDoc.name,
                            normalizedDoc.required,
                            normalizedDoc.id
                          )
                        }
                      />
                    </div>
                    {/* Document Status Manager */}
                    <div className="flex justify-end">
                      <DocumentStatusManager
                        document={status.document}
                        applicationId={applicationId}
                        onStatusUpdate={(
                          newStatus: DocumentStatus,
                          reason?: string
                        ) => {
                          // Update the document status in parent component
                          onDocumentStatusUpdate?.(
                            status.document.id,
                            newStatus,
                            reason
                          );
                        }}
                      />
                    </div>
                  </div>
                ) : (
                  <div className="space-y-3">
                    <div className="flex items-center gap-2 mb-2">
                      <Upload className="h-4 w-4 text-gray-500" />
                      <Label
                        htmlFor={`file-${normalizedDoc.name}`}
                        className="text-sm font-medium"
                      >
                        Upload {normalizedDoc.name}
                      </Label>
                    </div>
                    <div className="flex items-center gap-2">
                      <Input
                        id={`file-${normalizedDoc.name}`}
                        type="file"
                        accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                        onChange={(e) =>
                          handleFileInputChange(
                            e,
                            normalizedDoc.name,
                            normalizedDoc.required,
                            normalizedDoc.id
                          )
                        }
                        disabled={isUploading}
                        className="hidden"
                      />
                      <Button
                        variant={normalizedDoc.required ? "default" : "outline"}
                        size="sm"
                        disabled={isUploading}
                        className="px-3 py-2 h-9 text-xs font-medium"
                        onClick={() => {
                          const fileInput = document.getElementById(
                            `file-${normalizedDoc.name}`
                          ) as HTMLInputElement;
                          if (fileInput) {
                            fileInput.click();
                          }
                        }}
                      >
                        {isUploading ? (
                          <>
                            <RefreshCw className="h-3 w-3 animate-spin mr-1" />
                            Uploading
                          </>
                        ) : (
                          <>
                            <Upload className="h-3 w-3 mr-1" />
                            Upload
                          </>
                        )}
                      </Button>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Supported: PDF, DOC, DOCX, JPG, JPEG, PNG (Max 20MB)
                    </p>
                  </div>
                )}
              </div>
            );
          })}
        </CardContent>
      </Card>

      {/* Document Request Form */}
      <Card className="border-dashed border-2 border-gray-300 dark:border-gray-600 bg-gray-50/50 dark:bg-gray-800/50">
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-500 text-white rounded-lg">
                <FileText className="h-4 w-4" />
              </div>
              <div>
                <p className="font-medium text-gray-900 dark:text-gray-100">
                  Need Additional Documents?
                </p>
                <p className="text-sm text-muted-foreground">
                  Request new documents from the applicant
                </p>
              </div>
            </div>
            <DocumentRequestForm
              applicationId={applicationId}
              currentStageOrder={stageOrder}
              onDocumentRequested={() => {
                // Refresh the document list or trigger a re-fetch
                // This could be enhanced to update local state
              }}
            />
          </div>
        </CardContent>
      </Card>

      {/* Summary */}
      <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 border-blue-200 dark:border-blue-800">
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-500 text-white rounded-lg">
                <CheckCircle className="h-5 w-5" />
              </div>
              <div>
                <p className="font-medium text-blue-900 dark:text-blue-100">
                  Document Status
                </p>
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  {uploadedDocuments.length} of {documents.length} documents
                  uploaded
                </p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-sm text-blue-600 dark:text-blue-400 font-medium">
                Required Documents
              </p>
              <p className="font-bold text-lg text-blue-900 dark:text-blue-100">
                {
                  uploadedDocuments.filter((doc) =>
                    documents.find(
                      (d) =>
                        normalizeDocument(d).name === doc.document_name &&
                        normalizeDocument(d).required
                    )
                  ).length
                }{" "}
                of{" "}
                {documents.filter((d) => normalizeDocument(d).required).length}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
