"use client";

import React from "react";
import { ColumnDef } from "@tanstack/react-table";
import { But<PERSON> } from "@/components/ui/button";
import { format } from "date-fns";
import Link from "next/link";
import { Eye, UserPlus, ExternalLink, UserMinus } from "lucide-react";
import { EditablePriorityCell } from "./editable-priority-cell";
import { EditableWorkflowTemplateCell } from "./editable-workflow-template-cell";
import { EditableStatusCell } from "./editable-status-cell";

export const createApplicationsColumns = (
  onPriorityUpdate: (
    applicationId: string,
    newPriority: string
  ) => Promise<void>,
  onAssignAgent: (application: IApplication) => void,
  userRole?: "user" | "admin" | "agent",
  onWorkflowTemplateUpdate?: () => void,
  onStatusUpdate?: () => void,
  onRemoveAgent?: (application: IApplication, agent: IAgent) => void
): ColumnDef<IApplication>[] => [
  {
    id: "application_number",
    header: "Application #",
    cell: ({ row }) => {
      const application = row.original;
      return (
        <div className="font-medium w-[150px]">
          <Link
            href={`/applications/${application.id}`}
            className="text-primary hover:underline flex items-center gap-1"
          >
            {application.application_number}
            <ExternalLink className="h-3 w-3" />
          </Link>
        </div>
      );
    },
  },
  {
    id: "service_name",
    header: "Service Name",
    cell: ({ row }) => {
      const application = row.original;
      return (
        <div className="font-medium">{application.service_name || "N/A"}</div>
      );
    },
  },
  {
    id: "status",
    header: "Status",
    cell: ({ row }) => {
      const application = row.original;
      return (
        <EditableStatusCell
          applicationId={application.id}
          currentStatus={application.status || "Draft"}
          onStatusUpdate={onStatusUpdate}
        />
      );
    },
  },
  {
    id: "priority",
    header: "Priority",
    cell: ({ row }) => {
      const application = row.original;
      return (
        <EditablePriorityCell
          applicationId={application.id}
          currentPriority={application.priority_level}
          onPriorityUpdate={onPriorityUpdate}
        />
      );
    },
  },

  // Only show assigned agent column for admin users
  ...(userRole === "admin"
    ? [
        {
          id: "assigned_agent",
          header: "Assigned Agent",
          cell: ({ row }: { row: any }) => {
            const application = row.original;

            // Check for new API format first (agent_ids), then fallback to legacy format (assigned_agent)
            let agents: IAgent[] = [];

            if (application.agent_ids && Array.isArray(application.agent_ids)) {
              // New API format - agent_ids is already an array of agent objects
              agents = application.agent_ids;
            } else if (application.assigned_agent) {
              // Legacy format - handle both single agent and array of agents
              agents = Array.isArray(application.assigned_agent)
                ? application.assigned_agent
                : [application.assigned_agent];
            }

            // Handle empty agent arrays or no agents assigned
            if (!agents || agents.length === 0) {
              return (
                <span className="text-muted-foreground text-sm">
                  Unassigned
                </span>
              );
            }

            // Display all agents with icons and removal buttons
            return (
              <div className="space-y-1 max-w-[200px]">
                {agents.map((agent, index) => (
                  <div
                    key={agent.id || index}
                    className="flex items-center justify-between gap-2"
                  >
                    <div className="font-medium text-xs truncate min-w-0 flex-1">
                      {agent.name}
                    </div>
                    {/* Show remove button only for admin users */}
                    {userRole === "admin" && onRemoveAgent && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onRemoveAgent(application, agent)}
                        className="h-6 w-6 p-0 hover:bg-destructive/10 hover:text-destructive flex-shrink-0"
                        title={`Remove Agent ${agent.name}`}
                      >
                        <UserMinus className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                ))}
              </div>
            );
          },
        },
      ]
    : []),
  {
    id: "user_details",
    header: "User Details",
    cell: ({ row }) => {
      const application = row.original;

      // Extract user information from different possible sources
      const name =
        (application as any).guest_name ||
        application.guest?.name ||
        (application as any).user?.name ||
        "N/A";

      const email =
        (application as any).guest_email ||
        application.guest?.email ||
        (application as any).user?.email ||
        "N/A";

      const mobile =
        (application as any).guest_mobile ||
        application.guest?.mobile ||
        (application as any).user?.mobile ||
        "N/A";

      return (
        <div className="space-y-1">
          <div className="font-medium text-sm">{name}</div>
          <div className="text-xs text-muted-foreground">{email}</div>
          <div className="text-xs text-muted-foreground">{mobile}</div>
        </div>
      );
    },
  },
  {
    id: "workflow_template",
    header: "Workflow Template",
    cell: ({ row }) => {
      const application = row.original;

      // Only show workflow template selector for admin users
      // if (userRole === "admin") {
      return (
        <EditableWorkflowTemplateCell
          applicationId={application.id}
          serviceId={application.service_id}
          currentWorkflowTemplateId={application.workflow_template?.id}
          currentWorkflowTemplateName={application.workflow_template?.name}
          onWorkflowTemplateUpdate={onWorkflowTemplateUpdate}
        />
      );
      // }

      // // For non-admin users, just show the template name
      // return (
      //   <div className="text-sm">
      //     {application.workflow_template?.name || "N/A"}
      //   </div>
      // );
    },
  },
  {
    id: "created_at",
    header: "Created",
    cell: ({ row }) => (
      <div className="text-sm">
        {format(new Date(row.original.created_at), "MMM dd, yyyy")}
      </div>
    ),
  },
  {
    id: "updated_at",
    header: "Updated",
    cell: ({ row }) => (
      <div className="text-sm">
        {format(new Date(row.original.updated_at), "MMM dd, yyyy")}
      </div>
    ),
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const application = row.original;
      return (
        <div className="flex items-center gap-1 w-[120px]">
          {/* Show Assign Agent button only for admin users */}
          {userRole === "admin" && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onAssignAgent(application)}
              className="h-8 w-8 p-0"
              title="Assign Agent"
            >
              <UserPlus className="h-4 w-4" />
              <span className="sr-only">Assign agent</span>
            </Button>
          )}
          {/* Show View button for all users */}
          <Button variant="ghost" size="sm" asChild className="h-8 w-8 p-0">
            <Link href={`/applications/${application.id}`}>
              <Eye className="h-4 w-4" />
              <span className="sr-only">View application details</span>
            </Link>
          </Button>
        </div>
      );
    },
  },
];
