"use client";

import React, { useState } from "react";
import { format } from "date-fns";
import { Calendar as CalendarIcon, Clock, Edit2, Save, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { CalendarDatePicker } from "@/components/ui/calendar-datepicker";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useUpdateEstimatedCompletion } from "@/hooks/use-query";
import { cn } from "@/lib/utils";

interface EstimatedCompletionFieldProps {
  applicationId: string;
  currentDate?: string | null;
  className?: string;
}

export const EstimatedCompletionField: React.FC<
  EstimatedCompletionFieldProps
> = ({ applicationId, currentDate, className }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(
    currentDate ? new Date(currentDate) : undefined
  );
  const [isPopoverOpen, setIsPopoverOpen] = useState(false);

  const updateEstimatedCompletion = useUpdateEstimatedCompletion(applicationId);

  const handleSave = async () => {
    if (!selectedDate) return;

    try {
      await updateEstimatedCompletion.mutateAsync({
        estimated_completion: selectedDate.toISOString(),
      });
      setIsEditing(false);
      setIsPopoverOpen(false);
    } catch (error) {
      console.error("Failed to update estimated completion:", error);
    }
  };

  const handleCancel = () => {
    setSelectedDate(currentDate ? new Date(currentDate) : undefined);
    setIsEditing(false);
    setIsPopoverOpen(false);
  };

  const handleEdit = () => {
    setIsEditing(true);
  };

  return (
    <div
      className={cn(
        "group p-4 rounded-lg bg-gradient-to-br from-gray-50 to-gray-100/50 dark:from-gray-800/50 dark:to-gray-700/50 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-200",
        className
      )}
    >
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-blue-500 text-white rounded-lg group-hover:scale-110 transition-transform duration-200">
            <Clock className="h-4 w-4" />
          </div>
          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
            Estimated Completion
          </p>
        </div>
        {!isEditing && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleEdit}
            className="opacity-0 group-hover:opacity-100 transition-opacity duration-200"
          >
            <Edit2 className="h-3 w-3" />
          </Button>
        )}
      </div>

      {isEditing ? (
        <div className="space-y-3">
          <Popover open={isPopoverOpen} onOpenChange={setIsPopoverOpen}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "w-full justify-start text-left font-normal",
                  !selectedDate && "text-muted-foreground"
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {selectedDate ? (
                  format(selectedDate, "PPP")
                ) : (
                  <span>Pick a date</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <CalendarDatePicker
                mode="single"
                selected={selectedDate}
                onSelect={setSelectedDate}
                disabled={(date) => date < new Date()}
                initialFocus
              />
            </PopoverContent>
          </Popover>

          <div className="flex gap-2">
            <Button
              size="sm"
              onClick={handleSave}
              disabled={updateEstimatedCompletion.isPending || !selectedDate}
              className="flex-1"
            >
              <Save className="h-3 w-3 mr-1" />
              {updateEstimatedCompletion.isPending ? "Saving..." : "Save"}
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={handleCancel}
              disabled={updateEstimatedCompletion.isPending}
              className="flex-1"
            >
              <X className="h-3 w-3 mr-1" />
              Cancel
            </Button>
          </div>
        </div>
      ) : (
        <p className="text-base font-semibold text-gray-900 dark:text-gray-100">
          {currentDate ? (
            format(new Date(currentDate), "PPP")
          ) : (
            <span className="text-gray-500 dark:text-gray-400 italic">
              Not set
            </span>
          )}
        </p>
      )}
    </div>
  );
};
