"use client";

import React, { useState } from "react";
import { Edit2, Save, X, FileText } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useUpdateNote } from "@/hooks/use-query";
import { cn } from "@/lib/utils";

interface NoteFieldProps {
  applicationId: string;
  currentNote?: string | null;
  className?: string;
}

export const NoteField: React.FC<NoteFieldProps> = ({
  applicationId,
  currentNote,
  className,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [noteValue, setNoteValue] = useState(currentNote || "");

  const updateNote = useUpdateNote(applicationId);

  const handleSave = async () => {
    try {
      await updateNote.mutateAsync({
        note: noteValue,
      });
      setIsEditing(false);
    } catch (error) {
      console.error("Failed to update note:", error);
    }
  };

  const handleCancel = () => {
    setNoteValue(currentNote || "");
    setIsEditing(false);
  };

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleAddNote = () => {
    setIsEditing(true);
    setNoteValue("");
  };

  return (
    <div
      className={cn(
        "group p-4 rounded-lg bg-gradient-to-br from-gray-50 to-gray-100/50 dark:from-gray-800/50 dark:to-gray-700/50 border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-200",
        className
      )}
    >
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-green-500 text-white rounded-lg group-hover:scale-110 transition-transform duration-200">
            <FileText className="h-4 w-4" />
          </div>
          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
            Note
          </p>
        </div>
        {!isEditing && currentNote && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleEdit}
            className="opacity-0 group-hover:opacity-100 transition-opacity duration-200"
          >
            <Edit2 className="h-3 w-3" />
          </Button>
        )}
      </div>

      {isEditing ? (
        <div className="space-y-3">
          <Textarea
            value={noteValue}
            onChange={(e) => setNoteValue(e.target.value)}
            placeholder="Enter note..."
            className="min-h-[100px] resize-none"
            disabled={updateNote.isPending}
          />

          <div className="flex gap-2">
            <Button
              size="sm"
              onClick={handleSave}
              disabled={updateNote.isPending}
              className="flex-1"
            >
              <Save className="h-3 w-3 mr-1" />
              {updateNote.isPending ? "Saving..." : "Save"}
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={handleCancel}
              disabled={updateNote.isPending}
              className="flex-1"
            >
              <X className="h-3 w-3 mr-1" />
              Cancel
            </Button>
          </div>
        </div>
      ) : (
        <div>
          {currentNote ? (
            <p className="text-base text-gray-900 dark:text-gray-100 whitespace-pre-wrap">
              {currentNote}
            </p>
          ) : (
            <div className="flex items-center justify-between">
              <span className="text-gray-500 dark:text-gray-400 italic">
                No note added
              </span>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleAddNote}
                className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 text-blue-600 hover:text-blue-700"
              >
                Add Note
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};
