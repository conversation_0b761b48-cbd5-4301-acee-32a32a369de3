"use client";

import React from "react";
import { cn } from "@/lib/utils";
import { Check, Circle, FileText, FormInput } from "lucide-react";
import { Button } from "@/components/ui/button";

interface WorkflowStepperProps {
  stages: IWorkflowStage[];
  currentStage: number;
  onStageChange: (stageIndex: number) => void;
}

export const WorkflowStepper: React.FC<WorkflowStepperProps> = ({
  stages,
  currentStage,
  onStageChange,
}) => {
  return (
    <div className="w-full">
      {/* Desktop Stepper */}
      <div className="hidden md:flex items-center justify-between">
        {stages.map((stage, index) => {
          const isCompleted = index < currentStage;
          const isCurrent = index === currentStage;
          const isUpcoming = index > currentStage;

          return (
            <React.Fragment key={index}>
              <div className="flex flex-col items-center">
                <Button
                  variant="ghost"
                  className={cn(
                    "h-12 w-12 rounded-full p-0 mb-2",
                    isCompleted && "bg-primary text-primary-foreground",
                    isCurrent && "bg-primary/10 border-2 border-primary",
                    isUpcoming && "bg-muted text-muted-foreground"
                  )}
                  onClick={() => onStageChange(index)}
                >
                  {isCompleted ? (
                    <Check className="h-6 w-6" />
                  ) : (
                    <Circle className="h-6 w-6" />
                  )}
                </Button>
                <div className="text-center max-w-24">
                  <p
                    className={cn(
                      "text-xs font-medium",
                      isCurrent && "text-primary",
                      isUpcoming && "text-muted-foreground"
                    )}
                  >
                    Step {index + 1}
                  </p>
                  <p
                    className={cn(
                      "text-xs truncate",
                      isCurrent && "text-primary font-medium",
                      isUpcoming && "text-muted-foreground"
                    )}
                    title={stage.stageName}
                  >
                    {stage.stageName}
                  </p>
                </div>
              </div>
              {index < stages.length - 1 && (
                <div
                  className={cn(
                    "flex-1 h-0.5 mx-4",
                    index < currentStage ? "bg-primary" : "bg-muted"
                  )}
                />
              )}
            </React.Fragment>
          );
        })}
      </div>

      {/* Mobile Stepper */}
      <div className="md:hidden space-y-4">
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <span>
            Step {currentStage + 1} of {stages.length}
          </span>
          <span>
            {Math.round(((currentStage + 1) / stages.length) * 100)}% Complete
          </span>
        </div>

        <div className="w-full bg-muted rounded-full h-2">
          <div
            className="bg-primary h-2 rounded-full transition-all duration-300"
            style={{ width: `${((currentStage + 1) / stages.length) * 100}%` }}
          />
        </div>

        <div className="space-y-2">
          {stages.map((stage, index) => {
            const isCompleted = index < currentStage;
            const isCurrent = index === currentStage;
            const isUpcoming = index > currentStage;

            return (
              <Button
                key={index}
                variant="ghost"
                className={cn(
                  "w-full justify-start h-auto p-3",
                  isCurrent && "bg-primary/10 border border-primary",
                  isCompleted && "bg-muted/50"
                )}
                onClick={() => onStageChange(index)}
              >
                <div className="flex items-center gap-3">
                  <div
                    className={cn(
                      "h-8 w-8 rounded-full flex items-center justify-center",
                      isCompleted && "bg-primary text-primary-foreground",
                      isCurrent && "bg-primary text-primary-foreground",
                      isUpcoming && "bg-muted text-muted-foreground"
                    )}
                  >
                    {isCompleted ? (
                      <Check className="h-4 w-4" />
                    ) : (
                      <span className="text-xs font-medium">{index + 1}</span>
                    )}
                  </div>
                  <div className="flex-1 text-left">
                    <p
                      className={cn(
                        "font-medium",
                        isCurrent && "text-primary",
                        isUpcoming && "text-muted-foreground"
                      )}
                    >
                      {stage.stageName}
                    </p>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      {stage.customFormRequired && (
                        <div className="flex items-center gap-1">
                          <FormInput className="h-3 w-3" />
                          <span>Form</span>
                        </div>
                      )}
                      {stage.documentsRequired && (
                        <div className="flex items-center gap-1">
                          <FileText className="h-3 w-3" />
                          <span>Documents</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </Button>
            );
          })}
        </div>
      </div>
    </div>
  );
};
