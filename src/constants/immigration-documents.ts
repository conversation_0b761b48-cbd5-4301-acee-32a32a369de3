// Immigration Document Categories
export const DOCUMENT_CATEGORIES = [
  { value: "Identity", label: "Identity" },
  { value: "Education", label: "Education" },
  { value: "Employment", label: "Employment" },
  { value: "Financial", label: "Financial" },
  { value: "Medical", label: "Medical" },
  { value: "Legal", label: "Legal" },
  { value: "Travel", label: "Travel" },
  { value: "Other", label: "Other" },
] as const;

export type DocumentCategory = (typeof DOCUMENT_CATEGORIES)[number]["value"];

// Document Category Descriptions
export const CATEGORY_DESCRIPTIONS = {
  Identity:
    "Documents for identity verification (passports, IDs, birth certificates, etc.)",
  Education:
    "Educational documents (diplomas, transcripts, certificates, etc.)",
  Employment:
    "Employment-related documents (CVs, work permits, employment letters, etc.)",
  Financial:
    "Financial documents (bank statements, tax returns, income proof, etc.)",
  Medical: "Medical documents (health certificates, vaccination records, etc.)",
  Legal: "Legal documents (marriage certificates, court documents, etc.)",
  Travel: "Travel-related documents (visas, travel insurance, etc.)",
  Other: "Other immigration-related documents",
} as const;
